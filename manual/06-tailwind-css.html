<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tailwind CSS - Web Development Manual</title>
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'custom-blue': '#1e40af',
                        'custom-purple': '#7c3aed'
                    }
                }
            }
        }
    </script>
    <style>
        .tailwind-demo {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body>
    <nav class="manual-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>Web Dev Manual</h2>
            </div>
            <ul class="nav-links">
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#utilities">Utility Classes</a></li>
                <li><a href="#components">Components</a></li>
                <li><a href="#responsive">Responsive Design</a></li>
                <li><a href="#customization">Customization</a></li>
                <li><a href="#resources">Resources</a></li>
            </ul>
            <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
        </div>
    </nav>

    <main class="manual-content">
        <header class="topic-header" id="introduction">
            <div class="header-content">
                <h1>Tailwind CSS</h1>
                <p class="header-description">
                    Master the utility-first CSS framework that enables rapid UI development with pre-built classes. 
                    Learn to build modern, responsive interfaces without writing custom CSS.
                </p>
                <div class="header-stats">
                    <span class="stat">⚡ Utility-First</span>
                    <span class="stat">📱 Mobile-First</span>
                    <span class="stat">🎨 Highly Customizable</span>
                </div>
            </div>
        </header>

        <section class="concepts" id="utilities">
            <h2>Core Utility Classes</h2>
            <p>Tailwind provides thousands of utility classes for styling elements directly in your HTML.</p>
            
            <div class="demo-container">
                <h3>Layout & Spacing</h3>
                <div class="tailwind-demo">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-100 rounded-lg">
                        <div class="bg-blue-500 text-white p-4 rounded">
                            <h4 class="font-bold text-lg mb-2">Flexbox</h4>
                            <div class="flex items-center justify-between">
                                <span class="bg-blue-700 px-2 py-1 rounded text-sm">Item 1</span>
                                <span class="bg-blue-700 px-2 py-1 rounded text-sm">Item 2</span>
                            </div>
                        </div>
                        
                        <div class="bg-green-500 text-white p-4 rounded">
                            <h4 class="font-bold text-lg mb-2">Grid</h4>
                            <div class="grid grid-cols-2 gap-2">
                                <div class="bg-green-700 p-2 rounded text-sm text-center">1</div>
                                <div class="bg-green-700 p-2 rounded text-sm text-center">2</div>
                                <div class="bg-green-700 p-2 rounded text-sm text-center">3</div>
                                <div class="bg-green-700 p-2 rounded text-sm text-center">4</div>
                            </div>
                        </div>
                        
                        <div class="bg-purple-500 text-white p-4 rounded">
                            <h4 class="font-bold text-lg mb-2">Spacing</h4>
                            <div class="space-y-2">
                                <div class="bg-purple-700 p-2 rounded text-sm">Margin & Padding</div>
                                <div class="bg-purple-700 p-2 rounded text-sm">Auto-spacing</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="code-block">
                    <pre><code>&lt;div class="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-100 rounded-lg"&gt;
    &lt;div class="bg-blue-500 text-white p-4 rounded"&gt;
        &lt;h4 class="font-bold text-lg mb-2"&gt;Flexbox&lt;/h4&gt;
        &lt;div class="flex items-center justify-between"&gt;
            &lt;span class="bg-blue-700 px-2 py-1 rounded text-sm"&gt;Item 1&lt;/span&gt;
            &lt;span class="bg-blue-700 px-2 py-1 rounded text-sm"&gt;Item 2&lt;/span&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div class="bg-green-500 text-white p-4 rounded"&gt;
        &lt;h4 class="font-bold text-lg mb-2"&gt;Grid&lt;/h4&gt;
        &lt;div class="grid grid-cols-2 gap-2"&gt;
            &lt;div class="bg-green-700 p-2 rounded text-sm text-center"&gt;1&lt;/div&gt;
            &lt;div class="bg-green-700 p-2 rounded text-sm text-center"&gt;2&lt;/div&gt;
            &lt;div class="bg-green-700 p-2 rounded text-sm text-center"&gt;3&lt;/div&gt;
            &lt;div class="bg-green-700 p-2 rounded text-sm text-center"&gt;4&lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Typography & Colors</h3>
                <div class="tailwind-demo">
                    <div class="bg-white p-6 rounded-lg shadow-lg border">
                        <h1 class="text-4xl font-bold text-gray-900 mb-4">Typography Scale</h1>
                        <h2 class="text-3xl font-semibold text-gray-800 mb-3">Heading 2</h2>
                        <h3 class="text-2xl font-medium text-gray-700 mb-2">Heading 3</h3>
                        <p class="text-lg text-gray-600 mb-4 leading-relaxed">
                            This is a paragraph with <span class="font-bold text-blue-600">bold blue text</span> and 
                            <span class="italic text-green-600">italic green text</span>. Tailwind makes typography easy.
                        </p>
                        
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">Red Badge</span>
                            <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">Yellow Badge</span>
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Green Badge</span>
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Blue Badge</span>
                        </div>
                        
                        <blockquote class="border-l-4 border-blue-500 pl-4 italic text-gray-600">
                            "Tailwind CSS is a utility-first CSS framework for rapidly building custom designs."
                        </blockquote>
                    </div>
                </div>
            </div>
        </section>

        <section class="examples" id="components">
            <h2>Building Components</h2>
            <p>Combine utility classes to create reusable components and complex UI elements.</p>
            
            <div class="demo-container">
                <h3>Card Components</h3>
                <div class="tailwind-demo">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Basic Card -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden">
                            <img src="https://via.placeholder.com/300x200/3B82F6/FFFFFF?text=Image" alt="Card image" class="w-full h-48 object-cover">
                            <div class="p-6">
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Basic Card</h3>
                                <p class="text-gray-600 mb-4">This is a simple card component built with Tailwind utilities.</p>
                                <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors">
                                    Learn More
                                </button>
                            </div>
                        </div>
                        
                        <!-- Profile Card -->
                        <div class="bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg shadow-lg p-6 text-white">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center text-purple-500 font-bold text-lg">
                                    JD
                                </div>
                                <div class="ml-4">
                                    <h3 class="font-semibold">John Doe</h3>
                                    <p class="text-purple-100 text-sm">Web Developer</p>
                                </div>
                            </div>
                            <p class="text-purple-100 mb-4">Building amazing web experiences with modern technologies.</p>
                            <div class="flex space-x-2">
                                <button class="bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded text-sm transition-all">
                                    Follow
                                </button>
                                <button class="bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded text-sm transition-all">
                                    Message
                                </button>
                            </div>
                        </div>
                        
                        <!-- Stats Card -->
                        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-green-500">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Revenue</h3>
                                <div class="bg-green-100 p-2 rounded-full">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="text-3xl font-bold text-gray-900 mb-2">$24,500</div>
                            <div class="flex items-center text-sm">
                                <span class="text-green-600 font-medium">+12.5%</span>
                                <span class="text-gray-500 ml-1">from last month</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-container">
                <h3>Form Components</h3>
                <div class="tailwind-demo">
                    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">Contact Form</h3>
                        <form class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter your name">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter your email">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                                <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter your message"></textarea>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label class="ml-2 block text-sm text-gray-700">
                                    I agree to the terms and conditions
                                </label>
                            </div>
                            
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                Send Message
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <section class="advanced" id="responsive">
            <h2>Responsive Design</h2>
            <p>Tailwind's mobile-first approach makes it easy to build responsive layouts with breakpoint prefixes.</p>
            
            <div class="demo-container">
                <h3>Responsive Grid</h3>
                <div class="tailwind-demo">
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 p-4 bg-gray-100 rounded-lg">
                        <div class="bg-blue-500 text-white p-4 rounded text-center">1</div>
                        <div class="bg-green-500 text-white p-4 rounded text-center">2</div>
                        <div class="bg-red-500 text-white p-4 rounded text-center">3</div>
                        <div class="bg-yellow-500 text-white p-4 rounded text-center">4</div>
                        <div class="bg-purple-500 text-white p-4 rounded text-center">5</div>
                        <div class="bg-pink-500 text-white p-4 rounded text-center">6</div>
                    </div>
                    <p class="text-sm text-gray-600 mt-2 text-center">
                        Resize your browser to see the responsive behavior
                    </p>
                </div>
                
                <div class="code-block">
                    <pre><code>&lt;div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4"&gt;
    &lt;div class="bg-blue-500 text-white p-4 rounded text-center"&gt;1&lt;/div&gt;
    &lt;div class="bg-green-500 text-white p-4 rounded text-center"&gt;2&lt;/div&gt;
    &lt;div class="bg-red-500 text-white p-4 rounded text-center"&gt;3&lt;/div&gt;
    &lt;div class="bg-yellow-500 text-white p-4 rounded text-center"&gt;4&lt;/div&gt;
    &lt;div class="bg-purple-500 text-white p-4 rounded text-center"&gt;5&lt;/div&gt;
    &lt;div class="bg-pink-500 text-white p-4 rounded text-center"&gt;6&lt;/div&gt;
&lt;/div&gt;

&lt;!-- Breakpoints:
sm: 640px
md: 768px  
lg: 1024px
xl: 1280px
2xl: 1536px
--&gt;</code></pre>
                </div>
            </div>
        </section>

        <section class="best-practices" id="customization">
            <h2>Customization & Configuration</h2>
            <p>Extend Tailwind with custom colors, fonts, and utilities to match your design system.</p>
            
            <div class="demo-container">
                <h3>Custom Configuration</h3>
                <div class="code-block">
                    <pre><code>// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{html,js}'],
  theme: {
    extend: {
      colors: {
        'brand-blue': '#1e40af',
        'brand-purple': '#7c3aed',
        'custom': {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a'
        }
      },
      fontFamily: {
        'custom': ['Inter', 'sans-serif']
      },
      spacing: {
        '72': '18rem',
        '84': '21rem',
        '96': '24rem'
      }
    }
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography')
  ]
}</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Custom Utilities</h3>
                <div class="code-block">
                    <pre><code>/* Custom CSS utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .gradient-text {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}</code></pre>
                </div>
            </div>
        </section>

        <section class="resources" id="resources">
            <h2>Additional Resources</h2>
            <div class="resource-grid">
                <div class="resource-card">
                    <h3>📖 Official Documentation</h3>
                    <ul>
                        <li><a href="https://tailwindcss.com/docs" target="_blank">Tailwind CSS Docs</a></li>
                        <li><a href="https://tailwindui.com/" target="_blank">Tailwind UI Components</a></li>
                        <li><a href="https://headlessui.com/" target="_blank">Headless UI</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>🛠️ Tools & Plugins</h3>
                    <ul>
                        <li><a href="https://tailwindcss.com/docs/editor-setup" target="_blank">Editor Setup</a></li>
                        <li><a href="https://github.com/tailwindlabs/tailwindcss-forms" target="_blank">Forms Plugin</a></li>
                        <li><a href="https://github.com/tailwindlabs/tailwindcss-typography" target="_blank">Typography Plugin</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>📚 Learning Resources</h3>
                    <ul>
                        <li><a href="https://tailwindcss.com/docs/utility-first" target="_blank">Utility-First Fundamentals</a></li>
                        <li><a href="https://www.youtube.com/tailwindlabs" target="_blank">Tailwind Labs YouTube</a></li>
                        <li><a href="https://tailwindcomponents.com/" target="_blank">Tailwind Components</a></li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <script src="assets/js/manual-scripts.js"></script>
</body>
</html>
