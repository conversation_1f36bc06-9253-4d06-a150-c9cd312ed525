<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GSAP Basics - Web Development Manual</title>
    <meta name="description" content="Learn GSAP (GreenSock Animation Platform) basics with interactive examples and practical tutorials">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Fira+Code:wght@300;400;500&display=swap" rel="stylesheet">
    
    <!-- GSAP CDN (Now 100% Free!) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js"></script>
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .code-font { font-family: 'Fira Code', monospace; }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- Navigation -->
    <nav class="manual-nav fixed top-0 left-0 right-0 z-50 px-6 py-4">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="index.html" class="text-2xl font-bold text-blue-600">WebDev Manual</a>
                <span class="text-sm text-gray-500">GSAP Basics</span>
            </div>
            
            <div class="hidden md:flex items-center space-x-6">
                <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
                <a href="#introduction" class="text-gray-700 hover:text-blue-600 transition-colors">Introduction</a>
                <a href="#basic-animations" class="text-gray-700 hover:text-blue-600 transition-colors">Basic Animations</a>
                <a href="#timelines" class="text-gray-700 hover:text-blue-600 transition-colors">Timelines</a>
                <a href="#interactive-demos" class="text-gray-700 hover:text-blue-600 transition-colors">Demos</a>
                <button id="theme-toggle" class="p-2 rounded-lg bg-gray-200 hover:bg-gray-300 transition-colors">
                    🌙
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="scroll-snap-item pt-24 pb-16 px-6">
        <div class="max-w-6xl mx-auto text-center">
            <div class="gsap-demo mb-8">
                <div class="animate-box" id="hero-gsap-box">
                    GSAP
                </div>
            </div>
            
            <h1 class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                GSAP Basics
            </h1>
            
            <p class="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto">
                Learn GreenSock Animation Platform (GSAP) - the professional JavaScript animation library that's now 100% free!
                Create stunning animations with ease and performance.
            </p>
            
            <div class="flex flex-wrap justify-center gap-4 mb-8">
                <span class="bg-green-600 text-white px-4 py-2 rounded-full text-sm font-semibold">100% Free</span>
                <span class="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">High Performance</span>
                <span class="bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Easy to Use</span>
                <span class="bg-orange-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Cross Browser</span>
            </div>
        </div>
    </section>

    <!-- Introduction -->
    <section id="introduction" class="scroll-snap-item py-16 px-6 bg-white">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">What is GSAP?</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h3 class="text-2xl font-semibold mb-6">GreenSock Animation Platform</h3>
                    <ul class="space-y-4 text-lg text-gray-700">
                        <li class="flex items-start">
                            <span class="text-green-500 mr-3">✓</span>
                            <span><strong>Professional-grade</strong> animation library used by major companies</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-3">✓</span>
                            <span><strong>Now 100% free</strong> - all plugins included (as of 2024)</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-3">✓</span>
                            <span><strong>High performance</strong> - up to 20x faster than jQuery</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-3">✓</span>
                            <span><strong>Cross-browser compatible</strong> - works everywhere</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-3">✓</span>
                            <span><strong>Powerful timeline</strong> system for complex animations</span>
                        </li>
                    </ul>
                    
                    <div class="mt-8">
                        <h4 class="text-xl font-semibold mb-4">Installation</h4>
                        <div class="code-block relative">
                            <div class="text-comment">// CDN (Recommended)</div>
                            <div>&lt;<span class="keyword">script</span> <span class="string">src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"</span>&gt;&lt;/<span class="keyword">script</span>&gt;</div>
                            <div>&lt;<span class="keyword">script</span> <span class="string">src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"</span>&gt;&lt;/<span class="keyword">script</span>&gt;</div>
                            <br>
                            <div class="text-comment">// NPM</div>
                            <div><span class="keyword">npm install</span> <span class="string">gsap</span></div>
                        </div>
                    </div>
                </div>
                
                <div class="demo-container">
                    <h4 class="text-xl font-semibold mb-4">Live Demo - Basic Animation</h4>
                    <div class="gsap-demo">
                        <div class="animate-box" id="intro-demo-box">
                            Watch me animate!
                        </div>
                    </div>
                    <button id="intro-demo-btn" class="mt-4 bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        Animate
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Basic Animations -->
    <section id="basic-animations" class="scroll-snap-item py-16 px-6">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">Basic Animation Methods</h2>
            
            <!-- gsap.to() -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">1. gsap.to() - Animate TO values</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">// Basic gsap.to() syntax</div>
                            <div><span class="function">gsap.to</span>(<span class="string">".box"</span>, {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">duration</span>: <span class="number">2</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">x</span>: <span class="number">300</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">y</span>: <span class="number">100</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">rotation</span>: <span class="number">360</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scale</span>: <span class="number">1.5</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">ease</span>: <span class="string">"power2.out"</span></div>
                            <div>});</div>
                        </div>
                    </div>
                    <div class="demo-container">
                        <div class="gsap-demo">
                            <div class="animate-box" id="to-demo-box">gsap.to()</div>
                        </div>
                        <button id="to-demo-btn" class="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Animate To
                        </button>
                    </div>
                </div>
            </div>

            <!-- gsap.from() -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">2. gsap.from() - Animate FROM values</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">// Basic gsap.from() syntax</div>
                            <div><span class="function">gsap.from</span>(<span class="string">".box"</span>, {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">duration</span>: <span class="number">2</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">x</span>: <span class="number">-300</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">opacity</span>: <span class="number">0</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scale</span>: <span class="number">0</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">ease</span>: <span class="string">"bounce.out"</span></div>
                            <div>});</div>
                        </div>
                    </div>
                    <div class="demo-container">
                        <div class="gsap-demo">
                            <div class="animate-box" id="from-demo-box">gsap.from()</div>
                        </div>
                        <button id="from-demo-btn" class="mt-4 bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                            Animate From
                        </button>
                    </div>
                </div>
            </div>

            <!-- gsap.fromTo() -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">3. gsap.fromTo() - Define both FROM and TO values</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">// Basic gsap.fromTo() syntax</div>
                            <div><span class="function">gsap.fromTo</span>(<span class="string">".box"</span>, {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">x</span>: <span class="number">-200</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">rotation</span>: <span class="number">-180</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scale</span>: <span class="number">0.5</span></div>
                            <div>}, {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">duration</span>: <span class="number">2</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">x</span>: <span class="number">200</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">rotation</span>: <span class="number">180</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scale</span>: <span class="number">1.2</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">ease</span>: <span class="string">"power3.inOut"</span></div>
                            <div>});</div>
                        </div>
                    </div>
                    <div class="demo-container">
                        <div class="gsap-demo">
                            <div class="animate-box" id="fromto-demo-box">gsap.fromTo()</div>
                        </div>
                        <button id="fromto-demo-btn" class="mt-4 bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            Animate FromTo
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Timelines -->
    <section id="timelines" class="scroll-snap-item py-16 px-6 bg-white">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">GSAP Timelines</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                    <h3 class="text-2xl font-semibold mb-6">Timeline Basics</h3>
                    <p class="text-lg text-gray-700 mb-6">
                        Timelines allow you to sequence animations and control them as a group. 
                        They're perfect for complex animation sequences.
                    </p>
                    
                    <div class="code-block relative mb-6">
                        <div class="text-comment">// Create a timeline</div>
                        <div><span class="keyword">const</span> <span class="function">tl</span> = <span class="function">gsap.timeline</span>();</div>
                        <br>
                        <div class="text-comment">// Add animations to timeline</div>
                        <div><span class="function">tl.to</span>(<span class="string">".box1"</span>, {<span class="keyword">x</span>: <span class="number">100</span>, <span class="keyword">duration</span>: <span class="number">1</span>})</div>
                        <div>&nbsp;&nbsp;.<span class="function">to</span>(<span class="string">".box2"</span>, {<span class="keyword">y</span>: <span class="number">100</span>, <span class="keyword">duration</span>: <span class="number">1</span>})</div>
                        <div>&nbsp;&nbsp;.<span class="function">to</span>(<span class="string">".box3"</span>, {<span class="keyword">rotation</span>: <span class="number">360</span>, <span class="keyword">duration</span>: <span class="number">1</span>});</div>
                        <br>
                        <div class="text-comment">// Control the timeline</div>
                        <div><span class="function">tl.play</span>();</div>
                        <div><span class="function">tl.pause</span>();</div>
                        <div><span class="function">tl.reverse</span>();</div>
                    </div>
                    
                    <h4 class="text-xl font-semibold mb-4">Timeline Positioning</h4>
                    <div class="code-block relative">
                        <div class="text-comment">// Positioning in timeline</div>
                        <div><span class="function">tl.to</span>(<span class="string">".box1"</span>, {<span class="keyword">x</span>: <span class="number">100</span>})</div>
                        <div>&nbsp;&nbsp;.<span class="function">to</span>(<span class="string">".box2"</span>, {<span class="keyword">y</span>: <span class="number">100</span>}, <span class="string">"-=0.5"</span>) <span class="text-comment">// Start 0.5s early</span></div>
                        <div>&nbsp;&nbsp;.<span class="function">to</span>(<span class="string">".box3"</span>, {<span class="keyword">rotation</span>: <span class="number">360</span>}, <span class="string">"&lt;"</span>); <span class="text-comment">// Start with previous</span></div>
                    </div>
                </div>
                
                <div class="demo-container">
                    <h4 class="text-xl font-semibold mb-4">Timeline Demo</h4>
                    <div class="gsap-demo">
                        <div class="animate-box" id="timeline-box1" style="position: absolute; left: 50px; top: 50px;">1</div>
                        <div class="animate-box" id="timeline-box2" style="position: absolute; left: 200px; top: 50px;">2</div>
                        <div class="animate-box" id="timeline-box3" style="position: absolute; left: 350px; top: 50px;">3</div>
                    </div>
                    <div class="flex gap-4 mt-6">
                        <button id="timeline-play-btn" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">Play</button>
                        <button id="timeline-pause-btn" class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">Pause</button>
                        <button id="timeline-reverse-btn" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Reverse</button>
                        <button id="timeline-restart-btn" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Restart</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Demos -->
    <section id="interactive-demos" class="scroll-snap-item py-16 px-6">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">Interactive Demos</h2>
            
            <!-- Stagger Animation -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">Stagger Animation</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">// Stagger multiple elements</div>
                            <div><span class="function">gsap.from</span>(<span class="string">".stagger-box"</span>, {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">duration</span>: <span class="number">1</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">y</span>: <span class="number">100</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">opacity</span>: <span class="number">0</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">stagger</span>: <span class="number">0.2</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">ease</span>: <span class="string">"back.out(1.7)"</span></div>
                            <div>});</div>
                        </div>
                    </div>
                    <div class="demo-container">
                        <div class="grid grid-cols-4 gap-4 mb-4">
                            <div class="animate-box stagger-box">1</div>
                            <div class="animate-box stagger-box">2</div>
                            <div class="animate-box stagger-box">3</div>
                            <div class="animate-box stagger-box">4</div>
                        </div>
                        <button id="stagger-demo-btn" class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                            Animate Stagger
                        </button>
                    </div>
                </div>
            </div>

            <!-- Text Animation -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">Text Animation</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">// Text plugin animation</div>
                            <div><span class="function">gsap.to</span>(<span class="string">".text-demo"</span>, {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">duration</span>: <span class="number">3</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">text</span>: <span class="string">"GSAP is amazing!"</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">ease</span>: <span class="string">"none"</span></div>
                            <div>});</div>
                            <br>
                            <div class="text-comment">// Split text character animation</div>
                            <div><span class="function">gsap.from</span>(<span class="string">".char"</span>, {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">duration</span>: <span class="number">1</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">y</span>: <span class="number">100</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">stagger</span>: <span class="number">0.05</span></div>
                            <div>});</div>
                        </div>
                    </div>
                    <div class="demo-container">
                        <div class="text-2xl font-bold text-center mb-4">
                            <span class="text-demo">Click to animate text!</span>
                        </div>
                        <button id="text-demo-btn" class="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                            Animate Text
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 50+ Animation Examples -->
    <section class="py-16 px-6 bg-gradient-to-br from-indigo-50 to-purple-50">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12 text-gray-900">50+ GSAP Animation Examples</h2>
            
            <!-- Movement & Position Animations -->
            <div class="mb-16">
                <h3 class="text-3xl font-semibold mb-8 text-purple-800">Movement & Position (15 Examples)</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Example 1: Slide In Left -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">1. Slide In Left</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="slide-left-box">Slide Left</div>
                        </div>
                        <button onclick="slideInLeft()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.from(".box", {x: -200, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 2: Slide In Right -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">2. Slide In Right</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="slide-right-box">Slide Right</div>
                        </div>
                        <button onclick="slideInRight()" class="mt-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.from(".box", {x: 200, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 3: Slide In Top -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">3. Slide In Top</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="slide-top-box">Slide Top</div>
                        </div>
                        <button onclick="slideInTop()" class="mt-4 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.from(".box", {y: -200, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 4: Slide In Bottom -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">4. Slide In Bottom</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="slide-bottom-box">Slide Bottom</div>
                        </div>
                        <button onclick="slideInBottom()" class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.from(".box", {y: 200, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 5: Diagonal Movement -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">5. Diagonal Movement</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="diagonal-box">Diagonal</div>
                        </div>
                        <button onclick="diagonalMove()" class="mt-4 bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {x: 100, y: 100, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 6: Circular Path -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">6. Circular Path</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="circular-box">Circle</div>
                        </div>
                        <button onclick="circularPath()" class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {rotation: 360, transformOrigin: "150px 50%"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 7: Zigzag Movement -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">7. Zigzag Movement</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="zigzag-box">Zigzag</div>
                        </div>
                        <button onclick="zigzagMove()" class="mt-4 bg-pink-600 text-white px-4 py-2 rounded hover:bg-pink-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>Timeline with alternating x/y movements</div>
                        </div>
                    </div>
                    
                    <!-- Example 8: Bounce In -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">8. Bounce In</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="bounce-in-box">Bounce In</div>
                        </div>
                        <button onclick="bounceIn()" class="mt-4 bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.from(".box", {scale: 0, ease: "bounce.out"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 9: Elastic In -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">9. Elastic In</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="elastic-in-box">Elastic In</div>
                        </div>
                        <button onclick="elasticIn()" class="mt-4 bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.from(".box", {x: -200, ease: "elastic.out(1,0.3)"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 10: Back In -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">10. Back In</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="back-in-box">Back In</div>
                        </div>
                        <button onclick="backIn()" class="mt-4 bg-cyan-600 text-white px-4 py-2 rounded hover:bg-cyan-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.from(".box", {x: -200, ease: "back.out(1.7)"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 11: Power Ease -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">11. Power Ease</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="power-ease-box">Power</div>
                        </div>
                        <button onclick="powerEase()" class="mt-4 bg-lime-600 text-white px-4 py-2 rounded hover:bg-lime-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {x: 200, ease: "power4.inOut"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 12: Sine Wave -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">12. Sine Wave</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="sine-wave-box">Sine</div>
                        </div>
                        <button onclick="sineWave()" class="mt-4 bg-emerald-600 text-white px-4 py-2 rounded hover:bg-emerald-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {x: 200, ease: "sine.inOut"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 13: Expo Ease -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">13. Expo Ease</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="expo-ease-box">Expo</div>
                        </div>
                        <button onclick="expoEase()" class="mt-4 bg-violet-600 text-white px-4 py-2 rounded hover:bg-violet-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {x: 200, ease: "expo.out"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 14: Circ Ease -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">14. Circ Ease</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="circ-ease-box">Circ</div>
                        </div>
                        <button onclick="circEase()" class="mt-4 bg-rose-600 text-white px-4 py-2 rounded hover:bg-rose-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {x: 200, ease: "circ.out"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 15: Custom Bezier -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">15. Custom Bezier</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="bezier-box">Bezier</div>
                        </div>
                        <button onclick="customBezier()" class="mt-4 bg-amber-600 text-white px-4 py-2 rounded hover:bg-amber-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>Custom cubic-bezier(0.68, -0.55, 0.265, 1.55)</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Scale & Transform Animations -->
            <div class="mb-16">
                <h3 class="text-3xl font-semibold mb-8 text-purple-800">Scale & Transform (15 Examples)</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Example 16: Scale Up -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">16. Scale Up</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="scale-up-box">Scale Up</div>
                        </div>
                        <button onclick="scaleUp()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {scale: 1.5, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 17: Scale Down -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">17. Scale Down</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="scale-down-box">Scale Down</div>
                        </div>
                        <button onclick="scaleDown()" class="mt-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {scale: 0.5, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 18: Scale X Only -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">18. Scale X Only</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="scale-x-box">Scale X</div>
                        </div>
                        <button onclick="scaleX()" class="mt-4 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {scaleX: 2, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 19: Scale Y Only -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">19. Scale Y Only</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="scale-y-box">Scale Y</div>
                        </div>
                        <button onclick="scaleY()" class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {scaleY: 2, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 20: Pulse Scale -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">20. Pulse Scale</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="pulse-scale-box">Pulse</div>
                        </div>
                        <button onclick="pulseScale()" class="mt-4 bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {scale: 1.3, yoyo: true, repeat: 3})</div>
                        </div>
                    </div>
                    
                    <!-- Example 21: Rotate 360 -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">21. Rotate 360°</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="rotate-360-box">Rotate</div>
                        </div>
                        <button onclick="rotate360()" class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {rotation: 360, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 22: Rotate Continuous -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">22. Continuous Rotation</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="rotate-continuous-box">Spin</div>
                        </div>
                        <button onclick="rotateContinuous()" class="mt-4 bg-pink-600 text-white px-4 py-2 rounded hover:bg-pink-700">
                            Toggle
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {rotation: "+=360", repeat: -1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 23: Skew X -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">23. Skew X</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="skew-x-box">Skew X</div>
                        </div>
                        <button onclick="skewX()" class="mt-4 bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {skewX: 30, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 24: Skew Y -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">24. Skew Y</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="skew-y-box">Skew Y</div>
                        </div>
                        <button onclick="skewY()" class="mt-4 bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {skewY: 30, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 25: Matrix Transform -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">25. Matrix Transform</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="matrix-box">Matrix</div>
                        </div>
                        <button onclick="matrixTransform()" class="mt-4 bg-cyan-600 text-white px-4 py-2 rounded hover:bg-cyan-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {transform: "matrix(1.5,0.5,-0.5,1.5,0,0)"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 26: Transform Origin -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">26. Transform Origin</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="transform-origin-box">Origin</div>
                        </div>
                        <button onclick="transformOrigin()" class="mt-4 bg-lime-600 text-white px-4 py-2 rounded hover:bg-lime-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {rotation: 45, transformOrigin: "top left"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 27: Flip Horizontal -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">27. Flip Horizontal</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="flip-h-box">Flip H</div>
                        </div>
                        <button onclick="flipHorizontal()" class="mt-4 bg-emerald-600 text-white px-4 py-2 rounded hover:bg-emerald-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {scaleX: -1, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 28: Flip Vertical -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">28. Flip Vertical</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="flip-v-box">Flip V</div>
                        </div>
                        <button onclick="flipVertical()" class="mt-4 bg-violet-600 text-white px-4 py-2 rounded hover:bg-violet-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {scaleY: -1, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 29: 3D Rotation X -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">29. 3D Rotation X</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="rotate-3d-x-box">3D X</div>
                        </div>
                        <button onclick="rotate3DX()" class="mt-4 bg-rose-600 text-white px-4 py-2 rounded hover:bg-rose-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {rotationX: 180, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 30: 3D Rotation Y -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">30. 3D Rotation Y</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="rotate-3d-y-box">3D Y</div>
                        </div>
                        <button onclick="rotate3DY()" class="mt-4 bg-amber-600 text-white px-4 py-2 rounded hover:bg-amber-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {rotationY: 180, duration: 1})</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Opacity & Visibility Examples -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-800 mb-4">🌟 Opacity & Visibility (15 Examples)</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Example 31: Fade In -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">31. Fade In</h4>
                        <div class="gsap-demo">
                            <div class="animate-box opacity-0" id="fade-in-box">Fade In</div>
                        </div>
                        <button onclick="fadeIn()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 1, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 32: Fade Out -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">32. Fade Out</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="fade-out-box">Fade Out</div>
                        </div>
                        <button onclick="fadeOut()" class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 0, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 33: Fade In Up -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">33. Fade In Up</h4>
                        <div class="gsap-demo">
                            <div class="animate-box opacity-0" id="fade-in-up-box" style="transform: translateY(50px);">Fade In Up</div>
                        </div>
                        <button onclick="fadeInUp()" class="mt-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 1, y: 0, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 34: Fade In Down -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">34. Fade In Down</h4>
                        <div class="gsap-demo">
                            <div class="animate-box opacity-0" id="fade-in-down-box" style="transform: translateY(-50px);">Fade In Down</div>
                        </div>
                        <button onclick="fadeInDown()" class="mt-4 bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 1, y: 0, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 35: Fade In Left -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">35. Fade In Left</h4>
                        <div class="gsap-demo">
                            <div class="animate-box opacity-0" id="fade-in-left-box" style="transform: translateX(-50px);">Fade In Left</div>
                        </div>
                        <button onclick="fadeInLeft()" class="mt-4 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 1, x: 0, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 36: Fade In Right -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">36. Fade In Right</h4>
                        <div class="gsap-demo">
                            <div class="animate-box opacity-0" id="fade-in-right-box" style="transform: translateX(50px);">Fade In Right</div>
                        </div>
                        <button onclick="fadeInRight()" class="mt-4 bg-pink-600 text-white px-4 py-2 rounded hover:bg-pink-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 1, x: 0, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 37: Fade In Scale -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">37. Fade In Scale</h4>
                        <div class="gsap-demo">
                            <div class="animate-box opacity-0" id="fade-in-scale-box" style="transform: scale(0.5);">Fade In Scale</div>
                        </div>
                        <button onclick="fadeInScale()" class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 1, scale: 1, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 38: Fade Out Scale -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">38. Fade Out Scale</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="fade-out-scale-box">Fade Out Scale</div>
                        </div>
                        <button onclick="fadeOutScale()" class="mt-4 bg-teal-600 text-white px-4 py-2 rounded hover:bg-teal-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 0, scale: 0.5, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 39: Fade In Rotate -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">39. Fade In Rotate</h4>
                        <div class="gsap-demo">
                            <div class="animate-box opacity-0" id="fade-in-rotate-box" style="transform: rotate(180deg);">Fade In Rotate</div>
                        </div>
                        <button onclick="fadeInRotate()" class="mt-4 bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 1, rotation: 0, duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 40: Fade Stagger -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">40. Fade Stagger</h4>
                        <div class="gsap-demo">
                            <div class="flex gap-2">
                                <div class="animate-box opacity-0 stagger-fade-box">1</div>
                                <div class="animate-box opacity-0 stagger-fade-box">2</div>
                                <div class="animate-box opacity-0 stagger-fade-box">3</div>
                            </div>
                        </div>
                        <button onclick="fadeStagger()" class="mt-4 bg-cyan-600 text-white px-4 py-2 rounded hover:bg-cyan-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".boxes", {opacity: 1, stagger: 0.2})</div>
                        </div>
                    </div>
                    
                    <!-- Example 41: Fade Pulse -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">41. Fade Pulse</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="fade-pulse-box">Fade Pulse</div>
                        </div>
                        <button onclick="fadePulse()" class="mt-4 bg-lime-600 text-white px-4 py-2 rounded hover:bg-lime-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 0.3, yoyo: true, repeat: 5})</div>
                        </div>
                    </div>
                    
                    <!-- Example 42: Fade Glow -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">42. Fade Glow</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="fade-glow-box">Fade Glow</div>
                        </div>
                        <button onclick="fadeGlow()" class="mt-4 bg-emerald-600 text-white px-4 py-2 rounded hover:bg-emerald-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {filter: "drop-shadow(0 0 20px #3b82f6)"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 43: Fade Blur -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">43. Fade Blur</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="fade-blur-box">Fade Blur</div>
                        </div>
                        <button onclick="fadeBlur()" class="mt-4 bg-violet-600 text-white px-4 py-2 rounded hover:bg-violet-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 0.5, filter: "blur(5px)"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 44: Fade Slide Combo -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">44. Fade Slide Combo</h4>
                        <div class="gsap-demo">
                            <div class="animate-box opacity-0" id="fade-slide-combo-box" style="transform: translateX(-100px) translateY(50px);">Fade Slide</div>
                        </div>
                        <button onclick="fadeSlideCombo()" class="mt-4 bg-rose-600 text-white px-4 py-2 rounded hover:bg-rose-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: 1, x: 0, y: 0, duration: 1.5})</div>
                        </div>
                    </div>
                    
                    <!-- Example 45: Fade Toggle -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">45. Fade Toggle</h4>
                        <div class="gsap-demo">
                            <div class="animate-box" id="fade-toggle-box">Fade Toggle</div>
                        </div>
                        <button onclick="fadeToggle()" class="mt-4 bg-amber-600 text-white px-4 py-2 rounded hover:bg-amber-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {opacity: opacity > 0.5 ? 0 : 1})</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Color & Background Examples -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-800 mb-4">🎨 Color & Background (5 Examples)</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Example 46: Color Change -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">46. Color Change</h4>
                        <div class="gsap-demo">
                            <div class="animate-box bg-blue-500" id="color-change-box">Color Change</div>
                        </div>
                        <button onclick="colorChange()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {backgroundColor: "#ef4444", duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 47: Color Cycle -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">47. Color Cycle</h4>
                        <div class="gsap-demo">
                            <div class="animate-box bg-red-500" id="color-cycle-box">Color Cycle</div>
                        </div>
                        <button onclick="colorCycle()" class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.timeline().to(".box", {backgroundColor: "#10b981"}).to(".box", {backgroundColor: "#3b82f6"})</div>
                        </div>
                    </div>
                    
                    <!-- Example 48: Border Radius -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">48. Border Radius</h4>
                        <div class="gsap-demo">
                            <div class="animate-box bg-green-500" id="border-radius-box">Border Radius</div>
                        </div>
                        <button onclick="borderRadius()" class="mt-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {borderRadius: "50%", duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 49: Shadow -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">49. Shadow</h4>
                        <div class="gsap-demo">
                            <div class="animate-box bg-yellow-500" id="shadow-box">Shadow</div>
                        </div>
                        <button onclick="shadowAnimate()" class="mt-4 bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {boxShadow: "0 20px 40px rgba(0,0,0,0.3)", duration: 1})</div>
                        </div>
                    </div>
                    
                    <!-- Example 50: Text Color -->
                    <div class="demo-container bg-white">
                        <h4 class="text-lg font-semibold mb-4">50. Text Color</h4>
                        <div class="gsap-demo">
                            <div class="animate-box bg-purple-500 text-white" id="text-color-box">Text Color</div>
                        </div>
                        <button onclick="textColor()" class="mt-4 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                            Animate
                        </button>
                        <div class="code-block mt-4 text-sm">
                            <div>gsap.to(".box", {color: "#fbbf24", duration: 1})</div>
                        </div>
                    </div>
                </div>
            </div>

    <!-- Resources -->
    <section class="py-16 px-6 bg-gray-900 text-white">
        <div class="max-w-6xl mx-auto text-center">
            <h2 class="text-4xl font-bold mb-8">Learn More</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div class="bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">Official Documentation</h3>
                    <p class="text-gray-300 mb-4">Complete GSAP documentation with examples</p>
                    <a href="https://gsap.com/docs/" target="_blank" class="text-green-400 hover:text-green-300">
                        Visit GSAP Docs →
                    </a>
                </div>
                
                <div class="bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">ScrollTrigger</h3>
                    <p class="text-gray-300 mb-4">Learn scroll-based animations</p>
                    <a href="29-scroll-animations.html" class="text-blue-400 hover:text-blue-300">
                        Next Tutorial →
                    </a>
                </div>
                
                <div class="bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">Advanced GSAP</h3>
                    <p class="text-gray-300 mb-4">Morphing, physics, and complex animations</p>
                    <a href="28-gsap-advanced.html" class="text-purple-400 hover:text-purple-300">
                        Advanced Guide →
                    </a>
                </div>
            </div>
            
            <p class="text-gray-400">
                GSAP is now 100% free thanks to Webflow's acquisition. Use it for commercial projects without restrictions!
            </p>
        </div>
    </section>

    <!-- Scripts -->
    <script src="assets/js/manual-scripts.js"></script>
    
    <script>
        // Initialize GSAP
        gsap.registerPlugin(ScrollTrigger, TextPlugin);
        
        // Hero animation
        gsap.from("#hero-gsap-box", {
            duration: 2,
            y: -200,
            rotation: 720,
            scale: 0.5,
            ease: "elastic.out(1, 0.3)"
        });
        
        // Introduction demo
        document.getElementById('intro-demo-btn').addEventListener('click', function() {
            gsap.to("#intro-demo-box", {
                duration: 1.5,
                x: 200,
                rotation: 360,
                scale: 1.2,
                backgroundColor: "#10b981",
                ease: "power2.out",
                yoyo: true,
                repeat: 1
            });
        });
        
        // Basic animation demos
        document.getElementById('to-demo-btn').addEventListener('click', function() {
            gsap.to("#to-demo-box", {
                duration: 2,
                x: 200,
                y: 50,
                rotation: 360,
                scale: 1.3,
                ease: "power2.out"
            });
        });
        
        document.getElementById('from-demo-btn').addEventListener('click', function() {
            gsap.set("#from-demo-box", {x: -200, opacity: 0, scale: 0});
            gsap.to("#from-demo-box", {
                duration: 2,
                x: 0,
                opacity: 1,
                scale: 1,
                ease: "bounce.out"
            });
        });
        
        document.getElementById('fromto-demo-btn').addEventListener('click', function() {
            gsap.fromTo("#fromto-demo-box", {
                x: -150,
                rotation: -180,
                scale: 0.5
            }, {
                duration: 2,
                x: 150,
                rotation: 180,
                scale: 1.2,
                ease: "power3.inOut"
            });
        });
        
        // Timeline demo
        const tl = gsap.timeline({paused: true});
        tl.to("#timeline-box1", {duration: 1, x: 100, rotation: 360})
          .to("#timeline-box2", {duration: 1, y: 100, scale: 1.5}, "-=0.5")
          .to("#timeline-box3", {duration: 1, x: -100, backgroundColor: "#ef4444"}, "<");
        
        document.getElementById('timeline-play-btn').addEventListener('click', () => tl.play());
        document.getElementById('timeline-pause-btn').addEventListener('click', () => tl.pause());
        document.getElementById('timeline-reverse-btn').addEventListener('click', () => tl.reverse());
        document.getElementById('timeline-restart-btn').addEventListener('click', () => tl.restart());
        
        // Stagger demo
        document.getElementById('stagger-demo-btn').addEventListener('click', function() {
            gsap.set(".stagger-box", {y: 100, opacity: 0});
            gsap.to(".stagger-box", {
                duration: 1,
                y: 0,
                opacity: 1,
                stagger: 0.2,
                ease: "back.out(1.7)"
            });
        });
        
        // Text demo
        document.getElementById('text-demo-btn').addEventListener('click', function() {
            gsap.to(".text-demo", {
                duration: 3,
                text: "GSAP is now 100% FREE!",
                ease: "none"
            });
        });
        
        // Scroll animations
        gsap.from(".topic-card", {
            scrollTrigger: {
                trigger: ".topic-card",
                start: "top 80%",
                toggleActions: "play none none reverse"
            },
            duration: 1,
            y: 50,
            opacity: 0,
            stagger: 0.2
        });
        
        // Animation Functions for Examples 1-30
        function slideInRight() {
            gsap.fromTo("#slide-right-box", {x: -200}, {x: 0, duration: 1});
        }
        
        function slideInLeft() {
            gsap.fromTo("#slide-left-box", {x: 200}, {x: 0, duration: 1});
        }
        
        function slideInTop() {
            gsap.fromTo("#slide-top-box", {y: -200}, {y: 0, duration: 1});
        }
        
        function slideInBottom() {
            gsap.fromTo("#slide-bottom-box", {y: 200}, {y: 0, duration: 1});
        }
        
        function diagonalMove() {
            gsap.to("#diagonal-box", {x: 150, y: -150, duration: 1});
        }
        
        function circularPath() {
            gsap.to("#circular-box", {
                rotation: 360,
                transformOrigin: "150px 50%",
                duration: 2
            });
        }
        
        function zigzagMove() {
            const tl = gsap.timeline();
            tl.to("#zigzag-box", {x: 100, duration: 0.5})
              .to("#zigzag-box", {y: -50, duration: 0.5})
              .to("#zigzag-box", {x: 200, duration: 0.5})
              .to("#zigzag-box", {y: 0, duration: 0.5});
        }
        
        function bounceIn() {
            gsap.fromTo("#bounce-in-box", {scale: 0}, {scale: 1, duration: 1, ease: "bounce.out"});
        }
        
        function elasticIn() {
            gsap.fromTo("#elastic-in-box", {x: -200}, {x: 0, duration: 1.5, ease: "elastic.out(1, 0.3)"});
        }
        
        function backIn() {
            gsap.fromTo("#back-in-box", {x: -200}, {x: 0, duration: 1, ease: "back.out(1.7)"});
        }
        
        function powerEase() {
            gsap.to("#power-ease-box", {x: 200, duration: 1, ease: "power4.inOut"});
        }
        
        function sineWave() {
            gsap.to("#sine-wave-box", {x: 200, duration: 1, ease: "sine.inOut"});
        }
        
        function expoEase() {
            gsap.to("#expo-ease-box", {x: 200, duration: 1, ease: "expo.out"});
        }
        
        function circEase() {
            gsap.to("#circ-ease-box", {x: 200, duration: 1, ease: "circ.out"});
        }
        
        function customBezier() {
            gsap.to("#bezier-box", {x: 200, duration: 1, ease: "power2.inOut"});
        }
        
        function scaleUp() {
            gsap.to("#scale-up-box", {scale: 1.5, duration: 1});
        }
        
        function scaleDown() {
            gsap.to("#scale-down-box", {scale: 0.5, duration: 1});
        }
        
        function scaleX() {
            gsap.to("#scale-x-box", {scaleX: 2, duration: 1});
        }
        
        function scaleY() {
            gsap.to("#scale-y-box", {scaleY: 2, duration: 1});
        }
        
        function rotate() {
            gsap.to("#rotate-box", {rotation: 360, duration: 1});
        }
        
        function rotateMultiple() {
            gsap.to("#rotate-multiple-box", {rotation: 720, duration: 2});
        }
        
        function skewX() {
            gsap.to("#skew-x-box", {skewX: 45, duration: 1});
        }
        
        function skewY() {
            gsap.to("#skew-y-box", {skewY: 45, duration: 1});
        }
        
        function transform3D() {
            gsap.to("#transform-3d-box", {
                rotationX: 45,
                rotationY: 45,
                z: 100,
                duration: 1.5
            });
        }
        
        function flipHorizontal() {
            gsap.to("#flip-h-box", {scaleX: -1, duration: 1});
        }
        
        function flipVertical() {
            gsap.to("#flip-v-box", {scaleY: -1, duration: 1});
        }
        
        function rotate3DX() {
            gsap.to("#rotate-3d-x-box", {rotationX: 180, duration: 1});
        }
        
        function rotate3DY() {
            gsap.to("#rotate-3d-y-box", {rotationY: 180, duration: 1});
        }
        
        // Missing Scale & Transform Functions
        function pulseScale() {
            gsap.to("#pulse-scale-box", {scale: 1.3, duration: 0.5, yoyo: true, repeat: 3});
        }
        
        function rotate360() {
            gsap.to("#rotate-360-box", {rotation: 360, duration: 1});
        }
        
        function rotateContinuous() {
            gsap.to("#rotate-continuous-box", {rotation: 360, duration: 2, repeat: -1, ease: "none"});
        }
        
        function matrixTransform() {
            gsap.to("#matrix-box", {
                rotation: 45,
                scaleX: 1.2,
                scaleY: 0.8,
                x: 50,
                y: -30,
                duration: 1
            });
        }
        
        function transformOrigin() {
            gsap.to("#transform-origin-box", {
                rotation: 90,
                transformOrigin: "top left",
                duration: 1
            });
        }
        
        // Opacity & Visibility Functions (Examples 31-45)
        function fadeIn() {
            gsap.to("#fade-in-box", {opacity: 1, duration: 1});
        }
        
        function fadeOut() {
            gsap.to("#fade-out-box", {opacity: 0, duration: 1});
        }
        
        function fadeInUp() {
            gsap.to("#fade-in-up-box", {opacity: 1, y: 0, duration: 1, ease: "power2.out"});
        }
        
        function fadeInDown() {
            gsap.to("#fade-in-down-box", {opacity: 1, y: 0, duration: 1, ease: "power2.out"});
        }
        
        function fadeInLeft() {
            gsap.to("#fade-in-left-box", {opacity: 1, x: 0, duration: 1, ease: "power2.out"});
        }
        
        function fadeInRight() {
            gsap.to("#fade-in-right-box", {opacity: 1, x: 0, duration: 1, ease: "power2.out"});
        }
        
        function fadeInScale() {
            gsap.to("#fade-in-scale-box", {opacity: 1, scale: 1, duration: 1, ease: "back.out(1.7)"});
        }
        
        function fadeOutScale() {
            gsap.to("#fade-out-scale-box", {opacity: 0, scale: 0.5, duration: 1, ease: "back.in(1.7)"});
        }
        
        function fadeInRotate() {
            gsap.to("#fade-in-rotate-box", {opacity: 1, rotation: 0, duration: 1, ease: "power2.out"});
        }
        
        function fadeStagger() {
            gsap.set(".stagger-fade-box", {opacity: 0});
            gsap.to(".stagger-fade-box", {opacity: 1, duration: 0.5, stagger: 0.2});
        }
        
        function fadePulse() {
            gsap.to("#fade-pulse-box", {opacity: 0.3, duration: 0.5, yoyo: true, repeat: 5});
        }
        
        function fadeGlow() {
            gsap.to("#fade-glow-box", {
                filter: "drop-shadow(0 0 20px #3b82f6)", 
                duration: 1, 
                yoyo: true, 
                repeat: 3
            });
        }
        
        function fadeBlur() {
            gsap.to("#fade-blur-box", {
                opacity: 0.5, 
                filter: "blur(5px)", 
                duration: 1, 
                yoyo: true, 
                repeat: 1
            });
        }
        
        function fadeSlideCombo() {
            gsap.to("#fade-slide-combo-box", {
                opacity: 1, 
                x: 0, 
                y: 0, 
                duration: 1.5, 
                ease: "power3.out"
            });
        }
        
        function fadeToggle() {
            const box = document.getElementById("fade-toggle-box");
            const currentOpacity = gsap.getProperty(box, "opacity");
            gsap.to(box, {
                opacity: currentOpacity > 0.5 ? 0 : 1, 
                duration: 1
            });
        }
        
        // Color & Background Functions (Examples 46-50)
        function colorChange() {
            gsap.to("#color-change-box", {backgroundColor: "#ef4444", duration: 1});
        }
        
        function colorCycle() {
            const tl = gsap.timeline();
            tl.to("#color-cycle-box", {backgroundColor: "#10b981", duration: 0.5})
              .to("#color-cycle-box", {backgroundColor: "#3b82f6", duration: 0.5})
              .to("#color-cycle-box", {backgroundColor: "#f59e0b", duration: 0.5})
              .to("#color-cycle-box", {backgroundColor: "#ef4444", duration: 0.5});
        }
        
        function borderRadius() {
            gsap.to("#border-radius-box", {borderRadius: "50%", duration: 1});
        }
        
        function shadowAnimate() {
            gsap.to("#shadow-box", {
                boxShadow: "0 20px 40px rgba(0,0,0,0.3)", 
                y: -10,
                duration: 1
            });
        }
        
        function textColor() {
            gsap.to("#text-color-box", {color: "#fbbf24", duration: 1});
        }
        
        // Helper function to make animations repeatable
        function makeRepeatable(selector, animation) {
            gsap.killTweensOf(selector);
            animation();
        }
    </script>
</body>
</html> 