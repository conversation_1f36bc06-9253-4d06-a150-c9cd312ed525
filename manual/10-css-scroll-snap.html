<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Scroll Snap - Web Development Manual</title>
    <meta name="description" content="Learn CSS Scroll Snap with interactive examples - create smooth scrolling experiences">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Fira+Code:wght@300;400;500&display=swap" rel="stylesheet">
    
    <!-- GSAP for enhanced animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .code-font { font-family: 'Fira Code', monospace; }
        
        /* Scroll Snap Examples */
        .scroll-snap-x {
            scroll-snap-type: x mandatory;
            overflow-x: auto;
            display: flex;
            gap: 1rem;
            padding: 1rem;
        }
        
        .scroll-snap-y {
            scroll-snap-type: y mandatory;
            overflow-y: auto;
            height: 300px;
        }
        
        .snap-item {
            scroll-snap-align: start;
            flex-shrink: 0;
            width: 250px;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .snap-item-y {
            scroll-snap-align: start;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            color: white;
        }
        
        .snap-item-y:nth-child(1) { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .snap-item-y:nth-child(2) { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .snap-item-y:nth-child(3) { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .snap-item-y:nth-child(4) { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        
        /* Gallery snap */
        .gallery-snap {
            scroll-snap-type: x mandatory;
            overflow-x: auto;
            display: flex;
            gap: 0;
        }
        
        .gallery-item {
            scroll-snap-align: center;
            flex-shrink: 0;
            width: 100%;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }
        
        .gallery-item:nth-child(1) { background: #ff6b6b; }
        .gallery-item:nth-child(2) { background: #4ecdc4; }
        .gallery-item:nth-child(3) { background: #45b7d1; }
        .gallery-item:nth-child(4) { background: #f9ca24; }
        .gallery-item:nth-child(5) { background: #6c5ce7; }
        
        /* Card snap */
        .card-snap {
            scroll-snap-type: x mandatory;
            overflow-x: auto;
            display: flex;
            gap: 1rem;
            padding: 1rem;
        }
        
        .card-item {
            scroll-snap-align: start;
            flex-shrink: 0;
            width: 300px;
            height: 200px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        /* Proximity snap */
        .proximity-snap {
            scroll-snap-type: x proximity;
            overflow-x: auto;
            display: flex;
            gap: 1rem;
            padding: 1rem;
        }
        
        .proximity-item {
            scroll-snap-align: center;
            flex-shrink: 0;
            width: 200px;
            height: 150px;
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        /* Hide scrollbars for demos */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- Navigation -->
    <nav class="manual-nav fixed top-0 left-0 right-0 z-50 px-6 py-4">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="index.html" class="text-2xl font-bold text-blue-600">WebDev Manual</a>
                <span class="text-sm text-gray-500">CSS Scroll Snap</span>
            </div>
            
            <div class="hidden md:flex items-center space-x-6">
                <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
                <a href="#basics" class="text-gray-700 hover:text-blue-600 transition-colors">Basics</a>
                <a href="#examples" class="text-gray-700 hover:text-blue-600 transition-colors">Examples</a>
                <a href="#advanced" class="text-gray-700 hover:text-blue-600 transition-colors">Advanced</a>
                <button id="theme-toggle" class="p-2 rounded-lg bg-gray-200 hover:bg-gray-300 transition-colors">
                    🌙
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-24 pb-16 px-6">
        <div class="max-w-6xl mx-auto text-center">
            <h1 class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                CSS Scroll Snap
            </h1>
            
            <p class="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto">
                Create smooth, controlled scrolling experiences with native CSS. 
                Perfect for carousels, galleries, and full-page scrolling.
            </p>
            
            <div class="flex flex-wrap justify-center gap-4 mb-8">
                <span class="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Native CSS</span>
                <span class="bg-green-600 text-white px-4 py-2 rounded-full text-sm font-semibold">No JavaScript</span>
                <span class="bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Mobile Friendly</span>
                <span class="bg-orange-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Performant</span>
            </div>
        </div>
    </section>

    <!-- Basics -->
    <section id="basics" class="py-16 px-6 bg-white">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">Scroll Snap Basics</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                    <h3 class="text-2xl font-semibold mb-6">What is CSS Scroll Snap?</h3>
                    <p class="text-lg text-gray-700 mb-6">
                        CSS Scroll Snap allows you to create smooth, controlled scrolling experiences 
                        by defining snap points where the scroll position should "snap" to.
                    </p>
                    
                    <h4 class="text-xl font-semibold mb-4">Key Properties</h4>
                    <ul class="space-y-3 text-gray-700">
                        <li><strong>scroll-snap-type:</strong> Defines snap behavior on container</li>
                        <li><strong>scroll-snap-align:</strong> Sets snap alignment on items</li>
                        <li><strong>scroll-snap-stop:</strong> Controls whether to skip snap points</li>
                        <li><strong>scroll-margin:</strong> Adds margin around snap areas</li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-xl font-semibold mb-4">Basic Setup</h4>
                    <div class="code-block relative">
                        <div class="text-comment">/* Container (scroll parent) */</div>
                        <div>.<span class="keyword">scroll-container</span> {</div>
                        <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-type</span>: <span class="string">x mandatory</span>;</div>
                        <div>&nbsp;&nbsp;<span class="keyword">overflow-x</span>: <span class="string">auto</span>;</div>
                        <div>&nbsp;&nbsp;<span class="keyword">display</span>: <span class="string">flex</span>;</div>
                        <div>}</div>
                        <br>
                        <div class="text-comment">/* Items (snap children) */</div>
                        <div>.<span class="keyword">scroll-item</span> {</div>
                        <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-align</span>: <span class="string">start</span>;</div>
                        <div>&nbsp;&nbsp;<span class="keyword">flex-shrink</span>: <span class="number">0</span>;</div>
                        <div>}</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Examples -->
    <section id="examples" class="py-16 px-6">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">Interactive Examples</h2>
            
            <!-- Horizontal Scroll Snap -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">1. Horizontal Scroll Snap (Mandatory)</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">/* Horizontal mandatory snap */</div>
                            <div>.<span class="keyword">scroll-snap-x</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-type</span>: <span class="string">x mandatory</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">overflow-x</span>: <span class="string">auto</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">display</span>: <span class="string">flex</span>;</div>
                            <div>}</div>
                            <br>
                            <div>.<span class="keyword">snap-item</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-align</span>: <span class="string">start</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">flex-shrink</span>: <span class="number">0</span>;</div>
                            <div>}</div>
                        </div>
                        <p class="text-gray-600 mt-4">
                            <strong>Mandatory:</strong> Always snaps to the nearest snap point
                        </p>
                    </div>
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">Try scrolling horizontally:</h4>
                        <div class="scroll-snap-x hide-scrollbar">
                            <div class="snap-item">Slide 1</div>
                            <div class="snap-item">Slide 2</div>
                            <div class="snap-item">Slide 3</div>
                            <div class="snap-item">Slide 4</div>
                            <div class="snap-item">Slide 5</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vertical Scroll Snap -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">2. Vertical Full-Page Scroll</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">/* Vertical full-page snap */</div>
                            <div>.<span class="keyword">scroll-snap-y</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-type</span>: <span class="string">y mandatory</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">overflow-y</span>: <span class="string">auto</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">height</span>: <span class="string">100vh</span>;</div>
                            <div>}</div>
                            <br>
                            <div>.<span class="keyword">snap-item-y</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-align</span>: <span class="string">start</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">height</span>: <span class="string">100vh</span>;</div>
                            <div>}</div>
                        </div>
                        <p class="text-gray-600 mt-4">
                            Perfect for full-page scrolling websites
                        </p>
                    </div>
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">Scroll vertically in the container:</h4>
                        <div class="scroll-snap-y hide-scrollbar">
                            <div class="snap-item-y">Section 1</div>
                            <div class="snap-item-y">Section 2</div>
                            <div class="snap-item-y">Section 3</div>
                            <div class="snap-item-y">Section 4</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gallery Snap -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">3. Image Gallery with Center Snap</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">/* Gallery with center alignment */</div>
                            <div>.<span class="keyword">gallery-snap</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-type</span>: <span class="string">x mandatory</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">overflow-x</span>: <span class="string">auto</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">display</span>: <span class="string">flex</span>;</div>
                            <div>}</div>
                            <br>
                            <div>.<span class="keyword">gallery-item</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-align</span>: <span class="string">center</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">flex-shrink</span>: <span class="number">0</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">width</span>: <span class="string">100%</span>;</div>
                            <div>}</div>
                        </div>
                        <p class="text-gray-600 mt-4">
                            <strong>Center align:</strong> Items snap to the center of the container
                        </p>
                    </div>
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">Swipe through gallery:</h4>
                        <div class="gallery-snap hide-scrollbar">
                            <div class="gallery-item">Image 1</div>
                            <div class="gallery-item">Image 2</div>
                            <div class="gallery-item">Image 3</div>
                            <div class="gallery-item">Image 4</div>
                            <div class="gallery-item">Image 5</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card Carousel -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">4. Card Carousel</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">/* Card carousel */</div>
                            <div>.<span class="keyword">card-snap</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-type</span>: <span class="string">x mandatory</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">overflow-x</span>: <span class="string">auto</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">display</span>: <span class="string">flex</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">gap</span>: <span class="string">1rem</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">padding</span>: <span class="string">1rem</span>;</div>
                            <div>}</div>
                            <br>
                            <div>.<span class="keyword">card-item</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-align</span>: <span class="string">start</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">flex-shrink</span>: <span class="number">0</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">width</span>: <span class="string">300px</span>;</div>
                            <div>}</div>
                        </div>
                    </div>
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">Scroll through cards:</h4>
                        <div class="card-snap hide-scrollbar">
                            <div class="card-item">
                                <h5 class="font-semibold text-lg">Card 1</h5>
                                <p class="text-gray-600">This is the first card with some content.</p>
                            </div>
                            <div class="card-item">
                                <h5 class="font-semibold text-lg">Card 2</h5>
                                <p class="text-gray-600">This is the second card with different content.</p>
                            </div>
                            <div class="card-item">
                                <h5 class="font-semibold text-lg">Card 3</h5>
                                <p class="text-gray-600">This is the third card with more content.</p>
                            </div>
                            <div class="card-item">
                                <h5 class="font-semibold text-lg">Card 4</h5>
                                <p class="text-gray-600">This is the fourth card with additional content.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Advanced -->
    <section id="advanced" class="py-16 px-6 bg-white">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">Advanced Techniques</h2>
            
            <!-- Proximity Snap -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">5. Proximity Snap</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">/* Proximity snap - more flexible */</div>
                            <div>.<span class="keyword">proximity-snap</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-type</span>: <span class="string">x proximity</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">overflow-x</span>: <span class="string">auto</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">display</span>: <span class="string">flex</span>;</div>
                            <div>}</div>
                            <br>
                            <div>.<span class="keyword">proximity-item</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-align</span>: <span class="string">center</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">flex-shrink</span>: <span class="number">0</span>;</div>
                            <div>}</div>
                        </div>
                        <p class="text-gray-600 mt-4">
                            <strong>Proximity:</strong> Only snaps if user scrolls close to a snap point
                        </p>
                    </div>
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">More flexible scrolling:</h4>
                        <div class="proximity-snap hide-scrollbar">
                            <div class="proximity-item">Item 1</div>
                            <div class="proximity-item">Item 2</div>
                            <div class="proximity-item">Item 3</div>
                            <div class="proximity-item">Item 4</div>
                            <div class="proximity-item">Item 5</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scroll Margin -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">6. Scroll Margin & Padding</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">/* Add margin around snap areas */</div>
                            <div>.<span class="keyword">snap-item</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-snap-align</span>: <span class="string">start</span>;</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-margin</span>: <span class="string">20px</span>;</div>
                            <div>}</div>
                            <br>
                            <div class="text-comment">/* Container padding */</div>
                            <div>.<span class="keyword">scroll-container</span> {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">scroll-padding</span>: <span class="string">20px</span>;</div>
                            <div>}</div>
                        </div>
                        
                        <h4 class="text-xl font-semibold mb-4 mt-8">Browser Support</h4>
                        <ul class="space-y-2 text-gray-700">
                            <li>✅ Chrome 69+</li>
                            <li>✅ Firefox 68+</li>
                            <li>✅ Safari 11+</li>
                            <li>✅ Edge 79+</li>
                            <li>✅ iOS Safari 11+</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-xl font-semibold mb-4">JavaScript Integration</h4>
                        <div class="code-block relative">
                            <div class="text-comment">// Programmatic scrolling</div>
                            <div><span class="keyword">const</span> <span class="function">scrollToItem</span> = (<span class="keyword">index</span>) => {</div>
                            <div>&nbsp;&nbsp;<span class="keyword">const</span> <span class="function">container</span> = <span class="function">document</span>.<span class="function">querySelector</span>(<span class="string">'.scroll-container'</span>);</div>
                            <div>&nbsp;&nbsp;<span class="keyword">const</span> <span class="function">item</span> = <span class="function">container</span>.<span class="function">children</span>[<span class="keyword">index</span>];</div>
                            <div>&nbsp;&nbsp;<span class="function">item</span>.<span class="function">scrollIntoView</span>({</div>
                            <div>&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">behavior</span>: <span class="string">'smooth'</span>,</div>
                            <div>&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">block</span>: <span class="string">'nearest'</span>,</div>
                            <div>&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">inline</span>: <span class="string">'start'</span></div>
                            <div>&nbsp;&nbsp;});</div>
                            <div>};</div>
                        </div>
                        
                        <h4 class="text-xl font-semibold mb-4 mt-8">Best Practices</h4>
                        <ul class="space-y-2 text-gray-700">
                            <li>• Use with touch devices in mind</li>
                            <li>• Test on different screen sizes</li>
                            <li>• Consider accessibility</li>
                            <li>• Provide visual indicators</li>
                            <li>• Combine with smooth scrolling</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 50+ Scroll Snap Examples -->
    <section class="py-16 px-6 bg-gray-50">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">50+ Scroll Snap Examples</h2>
            
            <!-- Horizontal Scroll Examples -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">🔄 Horizontal Scroll Examples (15 Examples)</h3>
                <div class="space-y-8">
                    <!-- Example 1: Basic Horizontal -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">1. Basic Horizontal Snap</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-red-500">Item 1</div>
                            <div class="snap-item bg-blue-500">Item 2</div>
                            <div class="snap-item bg-green-500">Item 3</div>
                            <div class="snap-item bg-yellow-500">Item 4</div>
                            <div class="snap-item bg-purple-500">Item 5</div>
                        </div>
                        <div class="code-block mt-4">
                            <code>scroll-snap-type: x mandatory; scroll-snap-align: start;</code>
                        </div>
                    </div>

                    <!-- Example 2: Center Aligned -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">2. Center Aligned Snap</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-pink-500" style="scroll-snap-align: center;">Item 1</div>
                            <div class="snap-item bg-indigo-500" style="scroll-snap-align: center;">Item 2</div>
                            <div class="snap-item bg-teal-500" style="scroll-snap-align: center;">Item 3</div>
                            <div class="snap-item bg-orange-500" style="scroll-snap-align: center;">Item 4</div>
                        </div>
                        <div class="code-block mt-4">
                            <code>scroll-snap-align: center;</code>
                        </div>
                    </div>

                    <!-- Example 3: End Aligned -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">3. End Aligned Snap</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-cyan-500" style="scroll-snap-align: end;">Item 1</div>
                            <div class="snap-item bg-lime-500" style="scroll-snap-align: end;">Item 2</div>
                            <div class="snap-item bg-rose-500" style="scroll-snap-align: end;">Item 3</div>
                            <div class="snap-item bg-violet-500" style="scroll-snap-align: end;">Item 4</div>
                        </div>
                        <div class="code-block mt-4">
                            <code>scroll-snap-align: end;</code>
                        </div>
                    </div>

                    <!-- Example 4: Proximity Snap -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">4. Proximity Snap</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x proximity;">
                            <div class="snap-item bg-amber-500" style="scroll-snap-align: start;">Item 1</div>
                            <div class="snap-item bg-emerald-500" style="scroll-snap-align: start;">Item 2</div>
                            <div class="snap-item bg-sky-500" style="scroll-snap-align: start;">Item 3</div>
                            <div class="snap-item bg-fuchsia-500" style="scroll-snap-align: start;">Item 4</div>
                        </div>
                        <div class="code-block mt-4">
                            <code>scroll-snap-type: x proximity;</code>
                        </div>
                    </div>

                    <!-- Example 5: Variable Width Items -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">5. Variable Width Items</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-red-600" style="width: 200px;">Small</div>
                            <div class="snap-item bg-blue-600" style="width: 350px;">Medium</div>
                            <div class="snap-item bg-green-600" style="width: 180px;">Tiny</div>
                            <div class="snap-item bg-yellow-600" style="width: 400px;">Large</div>
                        </div>
                        <div class="code-block mt-4">
                            <code>Variable width: 200px, 350px, 180px, 400px</code>
                        </div>
                    </div>

                    <!-- Example 6: Cards with Padding -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">6. Cards with Padding</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory; padding: 20px;">
                            <div class="snap-item bg-white border-2 border-gray-200" style="scroll-snap-align: start;">
                                <div class="text-gray-800 text-base">Card 1</div>
                                <div class="text-gray-600 text-sm">Description</div>
                            </div>
                            <div class="snap-item bg-white border-2 border-gray-200" style="scroll-snap-align: start;">
                                <div class="text-gray-800 text-base">Card 2</div>
                                <div class="text-gray-600 text-sm">Description</div>
                            </div>
                            <div class="snap-item bg-white border-2 border-gray-200" style="scroll-snap-align: start;">
                                <div class="text-gray-800 text-base">Card 3</div>
                                <div class="text-gray-600 text-sm">Description</div>
                            </div>
                        </div>
                        <div class="code-block mt-4">
                            <code>scroll-padding: 20px;</code>
                        </div>
                    </div>

                    <!-- Example 7: Rounded Items -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">7. Rounded Items</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-gradient-to-r from-purple-400 to-pink-400" style="border-radius: 50px;">Round 1</div>
                            <div class="snap-item bg-gradient-to-r from-blue-400 to-cyan-400" style="border-radius: 50px;">Round 2</div>
                            <div class="snap-item bg-gradient-to-r from-green-400 to-blue-400" style="border-radius: 50px;">Round 3</div>
                            <div class="snap-item bg-gradient-to-r from-yellow-400 to-orange-400" style="border-radius: 50px;">Round 4</div>
                        </div>
                        <div class="code-block mt-4">
                            <code>border-radius: 50px;</code>
                        </div>
                    </div>

                    <!-- Example 8: Image Gallery -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">8. Image Gallery Style</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory; gap: 0;">
                            <div class="snap-item bg-gradient-to-br from-red-500 to-pink-500" style="scroll-snap-align: center; width: 100%; margin: 0;">Image 1</div>
                            <div class="snap-item bg-gradient-to-br from-blue-500 to-purple-500" style="scroll-snap-align: center; width: 100%; margin: 0;">Image 2</div>
                            <div class="snap-item bg-gradient-to-br from-green-500 to-teal-500" style="scroll-snap-align: center; width: 100%; margin: 0;">Image 3</div>
                            <div class="snap-item bg-gradient-to-br from-yellow-500 to-orange-500" style="scroll-snap-align: center; width: 100%; margin: 0;">Image 4</div>
                        </div>
                        <div class="code-block mt-4">
                            <code>Full width gallery with center alignment</code>
                        </div>
                    </div>

                    <!-- Example 9: Testimonials -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">9. Testimonials</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-white shadow-lg" style="scroll-snap-align: start; padding: 2rem; text-align: left;">
                                <div class="text-gray-800 text-sm">"Great product!"</div>
                                <div class="text-gray-600 text-xs mt-2">- John Doe</div>
                            </div>
                            <div class="snap-item bg-white shadow-lg" style="scroll-snap-align: start; padding: 2rem; text-align: left;">
                                <div class="text-gray-800 text-sm">"Amazing service!"</div>
                                <div class="text-gray-600 text-xs mt-2">- Jane Smith</div>
                            </div>
                            <div class="snap-item bg-white shadow-lg" style="scroll-snap-align: start; padding: 2rem; text-align: left;">
                                <div class="text-gray-800 text-sm">"Highly recommend!"</div>
                                <div class="text-gray-600 text-xs mt-2">- Bob Johnson</div>
                            </div>
                        </div>
                        <div class="code-block mt-4">
                            <code>Testimonial cards with shadows</code>
                        </div>
                    </div>

                    <!-- Example 10: Product Showcase -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">10. Product Showcase</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-gradient-to-t from-gray-800 to-gray-600" style="scroll-snap-align: start;">
                                <div class="text-white text-lg">Product A</div>
                                <div class="text-gray-300 text-sm">$99.99</div>
                            </div>
                            <div class="snap-item bg-gradient-to-t from-gray-800 to-gray-600" style="scroll-snap-align: start;">
                                <div class="text-white text-lg">Product B</div>
                                <div class="text-gray-300 text-sm">$149.99</div>
                            </div>
                            <div class="snap-item bg-gradient-to-t from-gray-800 to-gray-600" style="scroll-snap-align: start;">
                                <div class="text-white text-lg">Product C</div>
                                <div class="text-gray-300 text-sm">$199.99</div>
                            </div>
                        </div>
                        <div class="code-block mt-4">
                            <code>Product cards with pricing</code>
                        </div>
                    </div>

                    <!-- Example 11: Team Members -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">11. Team Members</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-blue-100" style="scroll-snap-align: start;">
                                <div class="text-blue-800 text-lg">Alice</div>
                                <div class="text-blue-600 text-sm">Designer</div>
                            </div>
                            <div class="snap-item bg-green-100" style="scroll-snap-align: start;">
                                <div class="text-green-800 text-lg">Bob</div>
                                <div class="text-green-600 text-sm">Developer</div>
                            </div>
                            <div class="snap-item bg-purple-100" style="scroll-snap-align: start;">
                                <div class="text-purple-800 text-lg">Carol</div>
                                <div class="text-purple-600 text-sm">Manager</div>
                            </div>
                        </div>
                        <div class="code-block mt-4">
                            <code>Team member cards with roles</code>
                        </div>
                    </div>

                    <!-- Example 12: Stats Cards -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">12. Stats Cards</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-red-500" style="scroll-snap-align: start;">
                                <div class="text-white text-2xl font-bold">1.2K</div>
                                <div class="text-red-100 text-sm">Users</div>
                            </div>
                            <div class="snap-item bg-blue-500" style="scroll-snap-align: start;">
                                <div class="text-white text-2xl font-bold">3.4K</div>
                                <div class="text-blue-100 text-sm">Sales</div>
                            </div>
                            <div class="snap-item bg-green-500" style="scroll-snap-align: start;">
                                <div class="text-white text-2xl font-bold">5.6K</div>
                                <div class="text-green-100 text-sm">Views</div>
                            </div>
                        </div>
                        <div class="code-block mt-4">
                            <code>Statistics cards with metrics</code>
                        </div>
                    </div>

                    <!-- Example 13: Feature Cards -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">13. Feature Cards</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-gradient-to-br from-indigo-500 to-purple-600" style="scroll-snap-align: start;">
                                <div class="text-white text-lg">⚡ Fast</div>
                                <div class="text-indigo-100 text-sm">Lightning speed</div>
                            </div>
                            <div class="snap-item bg-gradient-to-br from-green-500 to-teal-600" style="scroll-snap-align: start;">
                                <div class="text-white text-lg">🔒 Secure</div>
                                <div class="text-green-100 text-sm">Bank-level security</div>
                            </div>
                            <div class="snap-item bg-gradient-to-br from-orange-500 to-red-600" style="scroll-snap-align: start;">
                                <div class="text-white text-lg">📱 Mobile</div>
                                <div class="text-orange-100 text-sm">Mobile-first design</div>
                            </div>
                        </div>
                        <div class="code-block mt-4">
                            <code>Feature highlights with icons</code>
                        </div>
                    </div>

                    <!-- Example 14: Blog Posts -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">14. Blog Posts</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-white border border-gray-200" style="scroll-snap-align: start; padding: 1.5rem; text-align: left;">
                                <div class="text-gray-800 text-base font-semibold">How to CSS</div>
                                <div class="text-gray-600 text-sm mt-1">Learn CSS basics...</div>
                                <div class="text-gray-400 text-xs mt-2">Jan 15, 2024</div>
                            </div>
                            <div class="snap-item bg-white border border-gray-200" style="scroll-snap-align: start; padding: 1.5rem; text-align: left;">
                                <div class="text-gray-800 text-base font-semibold">JavaScript Tips</div>
                                <div class="text-gray-600 text-sm mt-1">Advanced JS techniques...</div>
                                <div class="text-gray-400 text-xs mt-2">Jan 12, 2024</div>
                            </div>
                            <div class="snap-item bg-white border border-gray-200" style="scroll-snap-align: start; padding: 1.5rem; text-align: left;">
                                <div class="text-gray-800 text-base font-semibold">React Guide</div>
                                <div class="text-gray-600 text-sm mt-1">Getting started with React...</div>
                                <div class="text-gray-400 text-xs mt-2">Jan 10, 2024</div>
                            </div>
                        </div>
                        <div class="code-block mt-4">
                            <code>Blog post cards with dates</code>
                        </div>
                    </div>

                    <!-- Example 15: Portfolio Items -->
                    <div class="demo-container">
                        <h4 class="text-lg font-semibold mb-4">15. Portfolio Items</h4>
                        <div class="scroll-snap-x hide-scrollbar" style="scroll-snap-type: x mandatory;">
                            <div class="snap-item bg-gradient-to-br from-pink-400 to-red-400" style="scroll-snap-align: start;">
                                <div class="text-white text-lg">Project A</div>
                                <div class="text-pink-100 text-sm">Web Design</div>
                            </div>
                            <div class="snap-item bg-gradient-to-br from-blue-400 to-indigo-400" style="scroll-snap-align: start;">
                                <div class="text-white text-lg">Project B</div>
                                <div class="text-blue-100 text-sm">Mobile App</div>
                            </div>
                            <div class="snap-item bg-gradient-to-br from-green-400 to-teal-400" style="scroll-snap-align: start;">
                                <div class="text-white text-lg">Project C</div>
                                <div class="text-green-100 text-sm">Branding</div>
                            </div>
                        </div>
                        <div class="code-block mt-4">
                            <code>Portfolio showcase cards</code>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Resources -->
    <section class="py-16 px-6 bg-gray-900 text-white">
        <div class="max-w-6xl mx-auto text-center">
            <h2 class="text-4xl font-bold mb-8">Learn More</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div class="bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">MDN Documentation</h3>
                    <p class="text-gray-300 mb-4">Complete CSS Scroll Snap reference</p>
                    <a href="https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Scroll_Snap" target="_blank" class="text-blue-400 hover:text-blue-300">
                        Visit MDN →
                    </a>
                </div>
                
                <div class="bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">GSAP ScrollTrigger</h3>
                    <p class="text-gray-300 mb-4">Enhanced scroll animations</p>
                    <a href="29-scroll-animations.html" class="text-green-400 hover:text-green-300">
                        Next Tutorial →
                    </a>
                </div>
                
                <div class="bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">CSS Layouts</h3>
                    <p class="text-gray-300 mb-4">Flexbox and Grid fundamentals</p>
                    <a href="08-css-layouts.html" class="text-purple-400 hover:text-purple-300">
                        Learn Layouts →
                    </a>
                </div>
            </div>
            
            <p class="text-gray-400">
                CSS Scroll Snap is widely supported and provides native, performant scrolling experiences.
            </p>
        </div>
    </section>

    <!-- Scripts -->
    <script src="assets/js/manual-scripts.js"></script>
    
    <script>
        // Initialize GSAP for enhanced animations
        gsap.registerPlugin(ScrollTrigger);
        
        // Animate sections on scroll
        gsap.from(".demo-container", {
            scrollTrigger: {
                trigger: ".demo-container",
                start: "top 80%",
                toggleActions: "play none none reverse"
            },
            duration: 1,
            y: 50,
            opacity: 0,
            stagger: 0.2
        });
        
        // Add scroll indicators to snap containers
        document.querySelectorAll('.scroll-snap-x, .gallery-snap, .card-snap, .proximity-snap').forEach(container => {
            const indicator = document.createElement('div');
            indicator.className = 'flex justify-center mt-4 space-x-2';
            
            const itemCount = container.children.length;
            for (let i = 0; i < itemCount; i++) {
                const dot = document.createElement('div');
                dot.className = 'w-2 h-2 rounded-full bg-gray-300 cursor-pointer';
                dot.addEventListener('click', () => {
                    container.children[i].scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                        inline: 'start'
                    });
                });
                indicator.appendChild(dot);
            }
            
            container.parentNode.appendChild(indicator);
            
            // Update active dot on scroll
            container.addEventListener('scroll', () => {
                const scrollLeft = container.scrollLeft;
                const itemWidth = container.children[0].offsetWidth;
                const activeIndex = Math.round(scrollLeft / itemWidth);
                
                indicator.querySelectorAll('div').forEach((dot, index) => {
                    dot.className = index === activeIndex 
                        ? 'w-2 h-2 rounded-full bg-blue-500 cursor-pointer'
                        : 'w-2 h-2 rounded-full bg-gray-300 cursor-pointer';
                });
            });
        });
        
        // Smooth scroll polyfill for older browsers
        if (!('scrollBehavior' in document.documentElement.style)) {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/gh/iamdustan/smoothscroll@master/src/smoothscroll.js';
            document.head.appendChild(script);
        }
    </script>
</body>
</html> 