/* Web Development Manual - Custom Styles */

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Custom scroll snap */
.scroll-snap-container {
  scroll-snap-type: y mandatory;
  overflow-y: scroll;
  height: 100vh;
}

.scroll-snap-item {
  scroll-snap-align: start;
  min-height: 100vh;
}

/* Code syntax highlighting */
.code-block {
  background: #1e1e1e;
  color: #d4d4d4;
  border-radius: 8px;
  padding: 1rem;
  overflow-x: auto;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
}

.code-block .keyword { color: #569cd6; }
.code-block .string { color: #ce9178; }
.code-block .comment { color: #6a9955; }
.code-block .function { color: #dcdcaa; }
.code-block .number { color: #b5cea8; }

/* Interactive demo containers */
.demo-container {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem 0;
  background: #f9fafb;
}

.demo-container.dark {
  background: #1f2937;
  border-color: #374151;
  color: #f3f4f6;
}

/* GSAP Animation containers */
.gsap-demo {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.animate-box {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.animate-box:hover {
  transform: scale(1.05);
}

/* Three.js canvas container */
.threejs-container {
  width: 100%;
  height: 400px;
  border-radius: 12px;
  overflow: hidden;
  background: #000;
}

/* Navigation styles */
.manual-nav {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
}

.manual-nav.dark {
  background: rgba(31, 41, 55, 0.9);
  border-bottom-color: rgba(55, 65, 81, 0.8);
}

/* Topic cards */
.topic-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  background: white;
}

.topic-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.topic-card.dark {
  background: #1f2937;
  border-color: #374151;
}

/* Copy button */
.copy-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #374151;
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.copy-btn:hover {
  opacity: 1;
}

/* Scroll indicators */
.scroll-indicator {
  position: fixed;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  z-index: 50;
}

.scroll-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #d1d5db;
  margin: 0.5rem 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scroll-dot.active {
  background: #3b82f6;
  transform: scale(1.2);
}

/* Loading animations */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;
  }
  
  .scroll-indicator {
    display: none;
  }
  
  .threejs-container {
    height: 300px;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Print styles */
@media print {
  .manual-nav,
  .scroll-indicator,
  .copy-btn {
    display: none !important;
  }
} 