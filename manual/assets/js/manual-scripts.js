// Web Development Manual - Core JavaScript

// Initialize the manual
document.addEventListener('DOMContentLoaded', function() {
    initializeManual();
});

function initializeManual() {
    setupNavigation();
    setupCodeCopyButtons();
    setupScrollIndicators();
    setupThemeToggle();
    setupScrollSnap();
    setupLazyLoading();
    setupSearchFunctionality();
}

// Navigation setup
function setupNavigation() {
    const nav = document.querySelector('.manual-nav');
    if (!nav) return;

    // Smooth scroll for navigation links
    const navLinks = nav.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Hide/show navigation on scroll
    let lastScrollY = window.scrollY;
    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;
        if (currentScrollY > lastScrollY && currentScrollY > 100) {
            nav.style.transform = 'translateY(-100%)';
        } else {
            nav.style.transform = 'translateY(0)';
        }
        lastScrollY = currentScrollY;
    });
}

// Code copy functionality
function setupCodeCopyButtons() {
    const codeBlocks = document.querySelectorAll('.code-block');
    codeBlocks.forEach(block => {
        const copyBtn = document.createElement('button');
        copyBtn.className = 'copy-btn';
        copyBtn.innerHTML = '📋';
        copyBtn.title = 'Copy code';
        
        copyBtn.addEventListener('click', async () => {
            const code = block.textContent;
            try {
                await navigator.clipboard.writeText(code);
                copyBtn.innerHTML = '✅';
                setTimeout(() => {
                    copyBtn.innerHTML = '📋';
                }, 2000);
            } catch (err) {
                console.error('Failed to copy code:', err);
                copyBtn.innerHTML = '❌';
                setTimeout(() => {
                    copyBtn.innerHTML = '📋';
                }, 2000);
            }
        });
        
        block.style.position = 'relative';
        block.appendChild(copyBtn);
    });
}

// Scroll indicators
function setupScrollIndicators() {
    const sections = document.querySelectorAll('.scroll-snap-item, section[id]');
    if (sections.length === 0) return;

    const indicator = document.createElement('div');
    indicator.className = 'scroll-indicator';
    
    sections.forEach((section, index) => {
        const dot = document.createElement('div');
        dot.className = 'scroll-dot';
        dot.addEventListener('click', () => {
            section.scrollIntoView({ behavior: 'smooth' });
        });
        indicator.appendChild(dot);
    });
    
    document.body.appendChild(indicator);

    // Update active dot on scroll
    window.addEventListener('scroll', () => {
        let current = 0;
        sections.forEach((section, index) => {
            const rect = section.getBoundingClientRect();
            if (rect.top <= window.innerHeight / 2) {
                current = index;
            }
        });
        
        indicator.querySelectorAll('.scroll-dot').forEach((dot, index) => {
            dot.classList.toggle('active', index === current);
        });
    });
}

// Theme toggle
function setupThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    if (!themeToggle) return;

    const currentTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    
    themeToggle.addEventListener('click', () => {
        const theme = document.documentElement.getAttribute('data-theme');
        const newTheme = theme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        // Update theme classes
        document.querySelectorAll('.dark').forEach(el => {
            el.classList.toggle('dark', newTheme === 'dark');
        });
    });
}

// CSS Scroll Snap setup
function setupScrollSnap() {
    const container = document.querySelector('.scroll-snap-container');
    if (!container) return;

    // Smooth scroll polyfill for older browsers
    if (!('scrollBehavior' in document.documentElement.style)) {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/gh/iamdustan/smoothscroll@master/src/smoothscroll.js';
        document.head.appendChild(script);
    }
}

// Lazy loading for images and demos
function setupLazyLoading() {
    const lazyElements = document.querySelectorAll('[data-lazy]');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const src = element.getAttribute('data-lazy');
                
                if (element.tagName === 'IMG') {
                    element.src = src;
                } else {
                    element.style.backgroundImage = `url(${src})`;
                }
                
                element.removeAttribute('data-lazy');
                observer.unobserve(element);
            }
        });
    });
    
    lazyElements.forEach(el => observer.observe(el));
}

// Search functionality
function setupSearchFunctionality() {
    const searchInput = document.getElementById('search-input');
    if (!searchInput) return;

    const searchResults = document.getElementById('search-results');
    const searchableElements = document.querySelectorAll('h1, h2, h3, h4, p, .code-block');
    
    searchInput.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase().trim();
        
        if (query.length < 2) {
            if (searchResults) searchResults.innerHTML = '';
            return;
        }
        
        const results = [];
        searchableElements.forEach(element => {
            const text = element.textContent.toLowerCase();
            if (text.includes(query)) {
                results.push({
                    element,
                    text: element.textContent.substring(0, 100) + '...',
                    type: element.tagName.toLowerCase()
                });
            }
        });
        
        displaySearchResults(results, query);
    });
}

function displaySearchResults(results, query) {
    const searchResults = document.getElementById('search-results');
    if (!searchResults) return;
    
    if (results.length === 0) {
        searchResults.innerHTML = '<p class="text-gray-500 p-4">No results found</p>';
        return;
    }
    
    const html = results.slice(0, 10).map(result => `
        <div class="p-3 border-b border-gray-200 hover:bg-gray-50 cursor-pointer" 
             onclick="scrollToElement('${result.element.id || ''}')">
            <div class="text-sm text-blue-600 uppercase">${result.type}</div>
            <div class="text-gray-800">${highlightQuery(result.text, query)}</div>
        </div>
    `).join('');
    
    searchResults.innerHTML = html;
}

function highlightQuery(text, query) {
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
}

function scrollToElement(id) {
    if (!id) return;
    const element = document.getElementById(id);
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
    }
}

// GSAP Animation helpers
function initGSAPDemo(selector, animation) {
    if (typeof gsap === 'undefined') {
        console.warn('GSAP not loaded');
        return;
    }
    
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        animation(element);
    });
}

// Three.js helpers
function initThreeJSDemo(containerId, scene) {
    if (typeof THREE === 'undefined') {
        console.warn('Three.js not loaded');
        return;
    }
    
    const container = document.getElementById(containerId);
    if (!container) return;
    
    scene(container);
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Performance monitoring
function measurePerformance(name, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
}

// Export for use in other files
window.ManualJS = {
    initGSAPDemo,
    initThreeJSDemo,
    debounce,
    throttle,
    measurePerformance
}; 