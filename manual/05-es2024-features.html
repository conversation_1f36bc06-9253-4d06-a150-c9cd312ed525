<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ES2024 Features - Web Development Manual</title>
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .feature-showcase {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin: 1rem 0;
        }

        .compatibility-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            margin: 0.25rem;
        }

        .supported { background: #48bb78; color: white; }
        .partial { background: #ed8936; color: white; }
        .not-supported { background: #f56565; color: white; }

        .code-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }

        .before-after {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }

        .before-after h4 {
            background: #4a5568;
            color: white;
            margin: 0;
            padding: 0.75rem;
            font-size: 0.875rem;
        }

        .before-after .code-block {
            margin: 0;
            border-radius: 0;
        }

        .interactive-demo {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .demo-output {
            background: #1a202c;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Fira Code', monospace;
            margin: 1rem 0;
            min-height: 100px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <nav class="manual-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>Web Dev Manual</h2>
            </div>
            <ul class="nav-links">
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#array-methods">Array Methods</a></li>
                <li><a href="#object-features">Object Features</a></li>
                <li><a href="#async-features">Async Features</a></li>
                <li><a href="#pattern-matching">Pattern Matching</a></li>
                <li><a href="#resources">Resources</a></li>
            </ul>
            <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
        </div>
    </nav>

    <main class="manual-content">
        <header class="topic-header" id="introduction">
            <div class="header-content">
                <h1>ES2024 Features</h1>
                <p class="header-description">
                    Explore the latest JavaScript features introduced in ES2024 (ES15), including new array methods,
                    object enhancements, async improvements, and cutting-edge language features.
                </p>
                <div class="header-stats">
                    <span class="stat">🆕 Latest Features</span>
                    <span class="stat">🔬 Experimental APIs</span>
                    <span class="stat">⚡ Performance Improvements</span>
                </div>
            </div>
        </header>

        <section class="concepts" id="array-methods">
            <h2>New Array Methods</h2>
            <p>ES2024 introduces powerful new array methods that make data manipulation more intuitive and efficient.</p>

            <div class="feature-showcase">
                <h3>🎯 Array.prototype.toReversed()</h3>
                <p>Creates a new array with elements in reverse order without mutating the original array.</p>
                <div class="compatibility-badge supported">Chrome 110+</div>
                <div class="compatibility-badge supported">Firefox 115+</div>
                <div class="compatibility-badge supported">Safari 16+</div>
            </div>

            <div class="demo-container">
                <div class="code-comparison">
                    <div class="before-after">
                        <h4>❌ Old Way (Mutating)</h4>
                        <div class="code-block">
                            <pre><code>const numbers = [1, 2, 3, 4, 5];
const reversed = [...numbers].reverse();
console.log(numbers);   // [1, 2, 3, 4, 5]
console.log(reversed);  // [5, 4, 3, 2, 1]</code></pre>
                        </div>
                    </div>
                    <div class="before-after">
                        <h4>✅ New Way (Non-mutating)</h4>
                        <div class="code-block">
                            <pre><code>const numbers = [1, 2, 3, 4, 5];
const reversed = numbers.toReversed();
console.log(numbers);   // [1, 2, 3, 4, 5]
console.log(reversed);  // [5, 4, 3, 2, 1]</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="feature-showcase">
                <h3>🎯 Array.prototype.toSorted()</h3>
                <p>Creates a new sorted array without mutating the original array.</p>
            </div>

            <div class="demo-container">
                <div class="code-block">
                    <pre><code>const fruits = ['banana', 'apple', 'cherry', 'date'];
const sorted = fruits.toSorted();
const sortedByLength = fruits.toSorted((a, b) => a.length - b.length);

console.log(fruits);           // ['banana', 'apple', 'cherry', 'date']
console.log(sorted);           // ['apple', 'banana', 'cherry', 'date']
console.log(sortedByLength);   // ['date', 'apple', 'banana', 'cherry']

// Numeric sorting
const numbers = [10, 5, 40, 25, 1000, 1];
const numericSorted = numbers.toSorted((a, b) => a - b);
console.log(numericSorted);    // [1, 5, 10, 25, 40, 1000]</code></pre>
                </div>
            </div>

            <div class="feature-showcase">
                <h3>🎯 Array.prototype.toSpliced()</h3>
                <p>Creates a new array with some elements removed and/or replaced without mutating the original.</p>
            </div>

            <div class="demo-container">
                <div class="interactive-demo">
                    <h4>Try Array Methods:</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <textarea id="array-code" rows="10" style="width: 100%; font-family: 'Fira Code', monospace;">// Try the new array methods
const colors = ['red', 'green', 'blue', 'yellow'];

// toReversed()
console.log('Original:', colors);
console.log('Reversed:', colors.toReversed());

// toSorted()
console.log('Sorted:', colors.toSorted());

// toSpliced() - remove 1 element at index 1, add 'purple'
console.log('Spliced:', colors.toSpliced(1, 1, 'purple'));

// with() - replace element at index 2
console.log('With replacement:', colors.with(2, 'orange'));</textarea>
                            <button onclick="runArrayDemo()" style="margin-top: 0.5rem;">Run Code</button>
                        </div>
                        <div>
                            <div class="demo-output" id="array-output">Output will appear here...</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="examples" id="object-features">
            <h2>Object Enhancements</h2>
            <p>ES2024 brings new object methods and improvements for better object manipulation and property handling.</p>

            <div class="feature-showcase">
                <h3>🎯 Object.groupBy()</h3>
                <p>Groups array elements by a key function, returning an object with grouped results.</p>
                <div class="compatibility-badge partial">Chrome 117+</div>
                <div class="compatibility-badge not-supported">Firefox (Experimental)</div>
                <div class="compatibility-badge not-supported">Safari (Experimental)</div>
            </div>

            <div class="demo-container">
                <div class="code-block">
                    <pre><code>// Group people by age category
const people = [
    { name: 'Alice', age: 25 },
    { name: 'Bob', age: 35 },
    { name: 'Charlie', age: 28 },
    { name: 'Diana', age: 42 }
];

const groupedByAgeCategory = Object.groupBy(people, person => {
    if (person.age < 30) return 'young';
    if (person.age < 40) return 'middle';
    return 'senior';
});

console.log(groupedByAgeCategory);
// {
//   young: [{ name: 'Alice', age: 25 }, { name: 'Charlie', age: 28 }],
//   middle: [{ name: 'Bob', age: 35 }],
//   senior: [{ name: 'Diana', age: 42 }]
// }

// Group products by category
const products = [
    { name: 'Laptop', category: 'Electronics', price: 999 },
    { name: 'Shirt', category: 'Clothing', price: 29 },
    { name: 'Phone', category: 'Electronics', price: 699 },
    { name: 'Jeans', category: 'Clothing', price: 79 }
];

const productsByCategory = Object.groupBy(products, p => p.category);
console.log(productsByCategory);</code></pre>
                </div>
            </div>
        </section>

        <section class="advanced" id="async-features">
            <h2>Async/Await Enhancements</h2>
            <p>ES2024 introduces improvements to asynchronous programming with better error handling and control flow.</p>

            <div class="feature-showcase">
                <h3>🎯 Promise.withResolvers()</h3>
                <p>Provides a more convenient way to create promises with external resolve/reject functions.</p>
            </div>

            <div class="demo-container">
                <div class="code-comparison">
                    <div class="before-after">
                        <h4>❌ Old Way</h4>
                        <div class="code-block">
                            <pre><code>let resolve, reject;
const promise = new Promise((res, rej) => {
    resolve = res;
    reject = rej;
});

// Later in code...
setTimeout(() => {
    resolve('Success!');
}, 1000);</code></pre>
                        </div>
                    </div>
                    <div class="before-after">
                        <h4>✅ New Way</h4>
                        <div class="code-block">
                            <pre><code>const { promise, resolve, reject } = Promise.withResolvers();

// Later in code...
setTimeout(() => {
    resolve('Success!');
}, 1000);</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-container">
                <h3>Practical Example: Timeout with Cleanup</h3>
                <div class="code-block">
                    <pre><code>function createTimeoutPromise(ms) {
    const { promise, resolve, reject } = Promise.withResolvers();

    const timeoutId = setTimeout(() => {
        resolve(`Completed after ${ms}ms`);
    }, ms);

    // Add cleanup function
    promise.cleanup = () => {
        clearTimeout(timeoutId);
        reject(new Error('Cancelled'));
    };

    return promise;
}

// Usage
const timeoutPromise = createTimeoutPromise(2000);

// Cancel if needed
// timeoutPromise.cleanup();

timeoutPromise
    .then(result => console.log(result))
    .catch(error => console.log('Error:', error.message));</code></pre>
                </div>
            </div>
        </section>

        <section class="best-practices" id="pattern-matching">
            <h2>Advanced Language Features</h2>
            <p>Explore cutting-edge JavaScript features that are shaping the future of the language.</p>

            <div class="feature-showcase">
                <h3>🎯 Temporal API (Stage 3)</h3>
                <p>A modern date/time API that fixes the issues with the legacy Date object.</p>
                <div class="compatibility-badge not-supported">Experimental</div>
            </div>

            <div class="demo-container">
                <div class="code-block">
                    <pre><code>// Note: Temporal API is still experimental
// This is what it will look like when available

// Current date and time
const now = Temporal.Now.plainDateTimeISO();
console.log(now.toString()); // 2024-01-15T14:30:00

// Create specific dates
const birthday = Temporal.PlainDate.from('1990-05-15');
const meeting = Temporal.PlainDateTime.from('2024-01-20T09:00:00');

// Date arithmetic
const nextWeek = now.add({ days: 7 });
const age = now.since(birthday).years;

// Time zones
const utc = Temporal.Now.instant();
const tokyo = utc.toZonedDateTimeISO('Asia/Tokyo');
const newYork = utc.toZonedDateTimeISO('America/New_York');

// Duration calculations
const duration = meeting.since(now);
console.log(`Meeting in ${duration.hours} hours`);</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Interactive ES2024 Playground</h3>
                <div class="interactive-demo">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <textarea id="es2024-code" rows="15" style="width: 100%; font-family: 'Fira Code', monospace;">// Try ES2024 features
const data = [
    { name: 'Alice', department: 'Engineering', salary: 75000 },
    { name: 'Bob', department: 'Marketing', salary: 65000 },
    { name: 'Charlie', department: 'Engineering', salary: 80000 },
    { name: 'Diana', department: 'Sales', salary: 70000 }
];

// Group by department (polyfill included)
const byDepartment = groupBy(data, emp => emp.department);
console.log('Grouped by department:', byDepartment);

// Use new array methods
const names = data.map(emp => emp.name);
console.log('Names:', names);
console.log('Reversed names:', names.toReversed());
console.log('Sorted names:', names.toSorted());

// Promise.withResolvers example
const { promise, resolve } = Promise.withResolvers();
setTimeout(() => resolve('Async operation complete!'), 1000);
promise.then(result => console.log(result));</textarea>
                            <button onclick="runES2024Demo()" style="margin-top: 0.5rem;">Run Code</button>
                        </div>
                        <div>
                            <div class="demo-output" id="es2024-output">Output will appear here...</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="resources" id="resources">
            <h2>Additional Resources</h2>
            <div class="resource-grid">
                <div class="resource-card">
                    <h3>📖 Official Documentation</h3>
                    <ul>
                        <li><a href="https://tc39.es/ecma262/" target="_blank">ECMAScript 2024 Specification</a></li>
                        <li><a href="https://github.com/tc39/proposals" target="_blank">TC39 Proposals</a></li>
                        <li><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference" target="_blank">MDN JavaScript Reference</a></li>
                    </ul>
                </div>

                <div class="resource-card">
                    <h3>🛠️ Compatibility & Tools</h3>
                    <ul>
                        <li><a href="https://caniuse.com/" target="_blank">Can I Use</a></li>
                        <li><a href="https://babel.js.org/" target="_blank">Babel Transpiler</a></li>
                        <li><a href="https://github.com/zloirock/core-js" target="_blank">Core-js Polyfills</a></li>
                    </ul>
                </div>

                <div class="resource-card">
                    <h3>📚 Learning Resources</h3>
                    <ul>
                        <li><a href="https://2ality.com/" target="_blank">2ality Blog</a></li>
                        <li><a href="https://javascript.info/" target="_blank">JavaScript.info</a></li>
                        <li><a href="https://exploringjs.com/" target="_blank">Exploring JS</a></li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <script src="assets/js/manual-scripts.js"></script>
    <script>
        // Polyfill for Object.groupBy (for browsers that don't support it yet)
        if (!Object.groupBy) {
            Object.groupBy = function(items, keyFn) {
                return items.reduce((groups, item) => {
                    const key = keyFn(item);
                    if (!groups[key]) {
                        groups[key] = [];
                    }
                    groups[key].push(item);
                    return groups;
                }, {});
            };
        }

        // Polyfill for groupBy function
        function groupBy(items, keyFn) {
            return items.reduce((groups, item) => {
                const key = keyFn(item);
                if (!groups[key]) {
                    groups[key] = [];
                }
                groups[key].push(item);
                return groups;
            }, {});
        }

        // Polyfill for Promise.withResolvers (for browsers that don't support it yet)
        if (!Promise.withResolvers) {
            Promise.withResolvers = function() {
                let resolve, reject;
                const promise = new Promise((res, rej) => {
                    resolve = res;
                    reject = rej;
                });
                return { promise, resolve, reject };
            };
        }

        // Console simulation
        function createConsole(outputId) {
            const output = document.getElementById(outputId);
            return {
                log: (...args) => {
                    const message = args.map(arg =>
                        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                    ).join(' ');
                    output.textContent += message + '\n';
                },
                clear: () => {
                    output.textContent = '';
                }
            };
        }

        function runArrayDemo() {
            const code = document.getElementById('array-code').value;
            const console = createConsole('array-output');
            console.clear();

            try {
                // Add polyfills for new array methods if not supported
                if (!Array.prototype.toReversed) {
                    Array.prototype.toReversed = function() {
                        return [...this].reverse();
                    };
                }

                if (!Array.prototype.toSorted) {
                    Array.prototype.toSorted = function(compareFn) {
                        return [...this].sort(compareFn);
                    };
                }

                if (!Array.prototype.toSpliced) {
                    Array.prototype.toSpliced = function(start, deleteCount, ...items) {
                        const copy = [...this];
                        copy.splice(start, deleteCount, ...items);
                        return copy;
                    };
                }

                if (!Array.prototype.with) {
                    Array.prototype.with = function(index, value) {
                        const copy = [...this];
                        copy[index] = value;
                        return copy;
                    };
                }

                const func = new Function('console', code);
                func(console);
            } catch (error) {
                console.log('Error: ' + error.message);
            }
        }

        function runES2024Demo() {
            const code = document.getElementById('es2024-code').value;
            const console = createConsole('es2024-output');
            console.clear();

            try {
                const func = new Function('console', 'groupBy', 'Promise', code);
                func(console, groupBy, Promise);
            } catch (error) {
                console.log('Error: ' + error.message);
            }
        }

        // Initialize demos
        document.addEventListener('DOMContentLoaded', function() {
            runArrayDemo();
            runES2024Demo();
        });
    </script>
</body>
</html>