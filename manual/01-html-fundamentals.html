<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML5 Fundamentals - Web Development Manual</title>
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <nav class="manual-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>Web Dev Manual</h2>
            </div>
            <ul class="nav-links">
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#semantic-elements">Semantic Elements</a></li>
                <li><a href="#forms">Forms</a></li>
                <li><a href="#media">Media</a></li>
                <li><a href="#accessibility">Accessibility</a></li>
                <li><a href="#best-practices">Best Practices</a></li>
                <li><a href="#resources">Resources</a></li>
            </ul>
            <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
        </div>
    </nav>

    <main class="manual-content">
        <header class="topic-header" id="introduction">
            <div class="header-content">
                <h1>HTML5 Fundamentals</h1>
                <p class="header-description">
                    Master the foundation of web development with HTML5 semantic elements, forms, media, and accessibility features. 
                    This comprehensive guide covers everything from basic structure to advanced HTML5 APIs.
                </p>
                <div class="header-stats">
                    <span class="stat">📚 Complete Reference</span>
                    <span class="stat">🎯 Interactive Examples</span>
                    <span class="stat">♿ Accessibility Focused</span>
                </div>
            </div>
        </header>

        <section class="concepts" id="basic-structure">
            <h2>HTML Fundamentals: From Zero to Hero</h2>
            <p>Let's start from the absolute beginning and build up to complex HTML structures with 50+ progressive examples.</p>

            <!-- Example 1: Basic HTML Document -->
            <div class="demo-container">
                <h3>Example 1: Your First HTML Document</h3>
                <div class="code-block">
                    <pre><code>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;title&gt;My First Web Page&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    Hello World!
&lt;/body&gt;
&lt;/html&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <div style="border: 1px solid #ccc; padding: 1rem; background: white;">
                        Hello World!
                    </div>
                </div>
            </div>

            <!-- Example 2: Adding Language and Character Set -->
            <div class="demo-container">
                <h3>Example 2: Proper Document Setup</h3>
                <div class="code-block">
                    <pre><code>&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;title&gt;Better HTML Document&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    Welcome to proper HTML!
&lt;/body&gt;
&lt;/html&gt;</code></pre>
                </div>
                <p class="explanation">✅ Added language attribute and character encoding for better accessibility and internationalization.</p>
            </div>

            <!-- Example 3: Adding Viewport for Mobile -->
            <div class="demo-container">
                <h3>Example 3: Mobile-Responsive Setup</h3>
                <div class="code-block">
                    <pre><code>&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Mobile-Ready HTML&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    This page works on mobile devices!
&lt;/body&gt;
&lt;/html&gt;</code></pre>
                </div>
                <p class="explanation">✅ Added viewport meta tag for responsive design on mobile devices.</p>
            </div>

            <!-- Example 4: Basic Headings -->
            <div class="demo-container">
                <h3>Example 4: HTML Headings Hierarchy</h3>
                <div class="code-block">
                    <pre><code>&lt;h1&gt;Main Title (Most Important)&lt;/h1&gt;
&lt;h2&gt;Section Title&lt;/h2&gt;
&lt;h3&gt;Subsection Title&lt;/h3&gt;
&lt;h4&gt;Sub-subsection Title&lt;/h4&gt;
&lt;h5&gt;Minor Heading&lt;/h5&gt;
&lt;h6&gt;Smallest Heading&lt;/h6&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <h1 style="font-size: 2rem; margin: 0.5rem 0;">Main Title (Most Important)</h1>
                    <h2 style="font-size: 1.5rem; margin: 0.5rem 0;">Section Title</h2>
                    <h3 style="font-size: 1.25rem; margin: 0.5rem 0;">Subsection Title</h3>
                    <h4 style="font-size: 1.1rem; margin: 0.5rem 0;">Sub-subsection Title</h4>
                    <h5 style="font-size: 1rem; margin: 0.5rem 0;">Minor Heading</h5>
                    <h6 style="font-size: 0.9rem; margin: 0.5rem 0;">Smallest Heading</h6>
                </div>
                <p class="explanation">✅ Use headings in order (h1 → h2 → h3) for proper document structure and SEO.</p>
            </div>

            <!-- Example 5: Basic Paragraphs -->
            <div class="demo-container">
                <h3>Example 5: Paragraphs and Text</h3>
                <div class="code-block">
                    <pre><code>&lt;p&gt;This is a paragraph. It contains a complete thought or idea.&lt;/p&gt;
&lt;p&gt;This is another paragraph. Each paragraph should contain related sentences.&lt;/p&gt;
&lt;p&gt;Paragraphs are automatically spaced by browsers for better readability.&lt;/p&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <p>This is a paragraph. It contains a complete thought or idea.</p>
                    <p>This is another paragraph. Each paragraph should contain related sentences.</p>
                    <p>Paragraphs are automatically spaced by browsers for better readability.</p>
                </div>
            </div>

            <!-- Example 6: Text Formatting -->
            <div class="demo-container">
                <h3>Example 6: Basic Text Formatting</h3>
                <div class="code-block">
                    <pre><code>&lt;p&gt;This text has &lt;strong&gt;strong importance&lt;/strong&gt; (bold).&lt;/p&gt;
&lt;p&gt;This text has &lt;em&gt;emphasis&lt;/em&gt; (italic).&lt;/p&gt;
&lt;p&gt;This is &lt;mark&gt;highlighted text&lt;/mark&gt;.&lt;/p&gt;
&lt;p&gt;This is &lt;small&gt;small text&lt;/small&gt;.&lt;/p&gt;
&lt;p&gt;This is &lt;del&gt;deleted text&lt;/del&gt; and &lt;ins&gt;inserted text&lt;/ins&gt;.&lt;/p&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <p>This text has <strong>strong importance</strong> (bold).</p>
                    <p>This text has <em>emphasis</em> (italic).</p>
                    <p>This is <mark>highlighted text</mark>.</p>
                    <p>This is <small>small text</small>.</p>
                    <p>This is <del>deleted text</del> and <ins>inserted text</ins>.</p>
                </div>
                <p class="explanation">✅ Use semantic tags (strong, em) instead of visual tags (b, i) for better accessibility.</p>
            </div>

            <!-- Example 7: Line Breaks and Horizontal Rules -->
            <div class="demo-container">
                <h3>Example 7: Line Breaks and Dividers</h3>
                <div class="code-block">
                    <pre><code>&lt;p&gt;First line&lt;br&gt;Second line&lt;br&gt;Third line&lt;/p&gt;
&lt;hr&gt;
&lt;p&gt;Content after horizontal rule&lt;/p&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <p>First line<br>Second line<br>Third line</p>
                    <hr>
                    <p>Content after horizontal rule</p>
                </div>
                <p class="explanation">✅ Use &lt;br&gt; sparingly - usually CSS is better for spacing.</p>
            </div>

            <!-- Example 8: Basic Links -->
            <div class="demo-container">
                <h3>Example 8: Creating Links</h3>
                <div class="code-block">
                    <pre><code>&lt;p&gt;Visit &lt;a href="https://www.google.com"&gt;Google&lt;/a&gt;&lt;/p&gt;
&lt;p&gt;&lt;a href="https://www.github.com" target="_blank"&gt;GitHub (opens in new tab)&lt;/a&gt;&lt;/p&gt;
&lt;p&gt;&lt;a href="mailto:<EMAIL>"&gt;Send Email&lt;/a&gt;&lt;/p&gt;
&lt;p&gt;&lt;a href="tel:+1234567890"&gt;Call Us&lt;/a&gt;&lt;/p&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <p>Visit <a href="https://www.google.com" style="color: blue; text-decoration: underline;">Google</a></p>
                    <p><a href="https://www.github.com" target="_blank" style="color: blue; text-decoration: underline;">GitHub (opens in new tab)</a></p>
                    <p><a href="mailto:<EMAIL>" style="color: blue; text-decoration: underline;">Send Email</a></p>
                    <p><a href="tel:+1234567890" style="color: blue; text-decoration: underline;">Call Us</a></p>
                </div>
            </div>

            <!-- Example 9: Internal Links -->
            <div class="demo-container">
                <h3>Example 9: Internal Page Links</h3>
                <div class="code-block">
                    <pre><code>&lt;p&gt;&lt;a href="#section1"&gt;Go to Section 1&lt;/a&gt;&lt;/p&gt;
&lt;p&gt;&lt;a href="#section2"&gt;Go to Section 2&lt;/a&gt;&lt;/p&gt;

&lt;h2 id="section1"&gt;Section 1&lt;/h2&gt;
&lt;p&gt;Content of section 1...&lt;/p&gt;

&lt;h2 id="section2"&gt;Section 2&lt;/h2&gt;
&lt;p&gt;Content of section 2...&lt;/p&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <p><a href="#demo-section1" style="color: blue; text-decoration: underline;">Go to Section 1</a></p>
                    <p><a href="#demo-section2" style="color: blue; text-decoration: underline;">Go to Section 2</a></p>
                    <h3 id="demo-section1">Section 1</h3>
                    <p>Content of section 1...</p>
                    <h3 id="demo-section2">Section 2</h3>
                    <p>Content of section 2...</p>
                </div>
            </div>

            <!-- Example 10: Basic Images -->
            <div class="demo-container">
                <h3>Example 10: Adding Images</h3>
                <div class="code-block">
                    <pre><code>&lt;img src="https://via.placeholder.com/300x200" alt="Placeholder image"&gt;
&lt;img src="https://via.placeholder.com/150x150" alt="Small image" width="100" height="100"&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <img src="https://via.placeholder.com/300x200/4299e1/ffffff?text=Sample+Image" alt="Placeholder image" style="max-width: 100%; height: auto;">
                    <br><br>
                    <img src="https://via.placeholder.com/150x150/10b981/ffffff?text=Small" alt="Small image" width="100" height="100">
                </div>
                <p class="explanation">✅ Always include alt text for accessibility and SEO.</p>
            </div>
            <!-- Example 11: Unordered Lists -->
            <div class="demo-container">
                <h3>Example 11: Unordered Lists</h3>
                <div class="code-block">
                    <pre><code>&lt;ul&gt;
    &lt;li&gt;First item&lt;/li&gt;
    &lt;li&gt;Second item&lt;/li&gt;
    &lt;li&gt;Third item&lt;/li&gt;
&lt;/ul&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <ul>
                        <li>First item</li>
                        <li>Second item</li>
                        <li>Third item</li>
                    </ul>
                </div>
            </div>

            <!-- Example 12: Ordered Lists -->
            <div class="demo-container">
                <h3>Example 12: Ordered Lists</h3>
                <div class="code-block">
                    <pre><code>&lt;ol&gt;
    &lt;li&gt;Step one&lt;/li&gt;
    &lt;li&gt;Step two&lt;/li&gt;
    &lt;li&gt;Step three&lt;/li&gt;
&lt;/ol&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <ol>
                        <li>Step one</li>
                        <li>Step two</li>
                        <li>Step three</li>
                    </ol>
                </div>
            </div>

            <!-- Example 13: Nested Lists -->
            <div class="demo-container">
                <h3>Example 13: Nested Lists</h3>
                <div class="code-block">
                    <pre><code>&lt;ul&gt;
    &lt;li&gt;Fruits
        &lt;ul&gt;
            &lt;li&gt;Apple&lt;/li&gt;
            &lt;li&gt;Banana&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/li&gt;
    &lt;li&gt;Vegetables
        &lt;ul&gt;
            &lt;li&gt;Carrot&lt;/li&gt;
            &lt;li&gt;Broccoli&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/li&gt;
&lt;/ul&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <ul>
                        <li>Fruits
                            <ul>
                                <li>Apple</li>
                                <li>Banana</li>
                            </ul>
                        </li>
                        <li>Vegetables
                            <ul>
                                <li>Carrot</li>
                                <li>Broccoli</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Example 14: Description Lists -->
            <div class="demo-container">
                <h3>Example 14: Description Lists</h3>
                <div class="code-block">
                    <pre><code>&lt;dl&gt;
    &lt;dt&gt;HTML&lt;/dt&gt;
    &lt;dd&gt;HyperText Markup Language&lt;/dd&gt;

    &lt;dt&gt;CSS&lt;/dt&gt;
    &lt;dd&gt;Cascading Style Sheets&lt;/dd&gt;

    &lt;dt&gt;JavaScript&lt;/dt&gt;
    &lt;dd&gt;Programming language for web interactivity&lt;/dd&gt;
&lt;/dl&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <dl>
                        <dt><strong>HTML</strong></dt>
                        <dd>HyperText Markup Language</dd>

                        <dt><strong>CSS</strong></dt>
                        <dd>Cascading Style Sheets</dd>

                        <dt><strong>JavaScript</strong></dt>
                        <dd>Programming language for web interactivity</dd>
                    </dl>
                </div>
            </div>

            <!-- Example 15: Basic Table -->
            <div class="demo-container">
                <h3>Example 15: Simple Table</h3>
                <div class="code-block">
                    <pre><code>&lt;table&gt;
    &lt;tr&gt;
        &lt;th&gt;Name&lt;/th&gt;
        &lt;th&gt;Age&lt;/th&gt;
        &lt;th&gt;City&lt;/th&gt;
    &lt;/tr&gt;
    &lt;tr&gt;
        &lt;td&gt;John&lt;/td&gt;
        &lt;td&gt;25&lt;/td&gt;
        &lt;td&gt;New York&lt;/td&gt;
    &lt;/tr&gt;
    &lt;tr&gt;
        &lt;td&gt;Jane&lt;/td&gt;
        &lt;td&gt;30&lt;/td&gt;
        &lt;td&gt;London&lt;/td&gt;
    &lt;/tr&gt;
&lt;/table&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <table style="border-collapse: collapse; width: 100%;">
                        <tr>
                            <th style="border: 1px solid #ddd; padding: 8px; background: #f2f2f2;">Name</th>
                            <th style="border: 1px solid #ddd; padding: 8px; background: #f2f2f2;">Age</th>
                            <th style="border: 1px solid #ddd; padding: 8px; background: #f2f2f2;">City</th>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">John</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">25</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">New York</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">Jane</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">30</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">London</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Example 16: Table with Structure -->
            <div class="demo-container">
                <h3>Example 16: Proper Table Structure</h3>
                <div class="code-block">
                    <pre><code>&lt;table&gt;
    &lt;thead&gt;
        &lt;tr&gt;
            &lt;th&gt;Product&lt;/th&gt;
            &lt;th&gt;Price&lt;/th&gt;
            &lt;th&gt;Stock&lt;/th&gt;
        &lt;/tr&gt;
    &lt;/thead&gt;
    &lt;tbody&gt;
        &lt;tr&gt;
            &lt;td&gt;Laptop&lt;/td&gt;
            &lt;td&gt;$999&lt;/td&gt;
            &lt;td&gt;15&lt;/td&gt;
        &lt;/tr&gt;
        &lt;tr&gt;
            &lt;td&gt;Phone&lt;/td&gt;
            &lt;td&gt;$699&lt;/td&gt;
            &lt;td&gt;23&lt;/td&gt;
        &lt;/tr&gt;
    &lt;/tbody&gt;
    &lt;tfoot&gt;
        &lt;tr&gt;
            &lt;td&gt;Total Items&lt;/td&gt;
            &lt;td&gt;-&lt;/td&gt;
            &lt;td&gt;38&lt;/td&gt;
        &lt;/tr&gt;
    &lt;/tfoot&gt;
&lt;/table&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <table style="border-collapse: collapse; width: 100%;">
                        <thead>
                            <tr>
                                <th style="border: 1px solid #ddd; padding: 8px; background: #f2f2f2;">Product</th>
                                <th style="border: 1px solid #ddd; padding: 8px; background: #f2f2f2;">Price</th>
                                <th style="border: 1px solid #ddd; padding: 8px; background: #f2f2f2;">Stock</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">Laptop</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">$999</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">15</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">Phone</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">$699</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">23</td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr style="background: #f9f9f9;">
                                <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">Total Items</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">-</td>
                                <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">38</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <p class="explanation">✅ Use thead, tbody, and tfoot for better table structure and accessibility.</p>
            </div>

            <!-- Example 17: Basic Form -->
            <div class="demo-container">
                <h3>Example 17: Your First Form</h3>
                <div class="code-block">
                    <pre><code>&lt;form&gt;
    &lt;label for="name"&gt;Name:&lt;/label&gt;
    &lt;input type="text" id="name" name="name"&gt;

    &lt;label for="email"&gt;Email:&lt;/label&gt;
    &lt;input type="email" id="email" name="email"&gt;

    &lt;button type="submit"&gt;Submit&lt;/button&gt;
&lt;/form&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <form style="display: flex; flex-direction: column; gap: 10px; max-width: 300px;">
                        <label for="demo-name">Name:</label>
                        <input type="text" id="demo-name" name="name" style="padding: 8px; border: 1px solid #ccc;">

                        <label for="demo-email">Email:</label>
                        <input type="email" id="demo-email" name="email" style="padding: 8px; border: 1px solid #ccc;">

                        <button type="submit" style="padding: 10px; background: #007bff; color: white; border: none; cursor: pointer;">Submit</button>
                    </form>
                </div>
                <p class="explanation">✅ Always connect labels to inputs using the 'for' attribute and 'id'.</p>
            </div>

            <!-- Example 18: Different Input Types -->
            <div class="demo-container">
                <h3>Example 18: HTML5 Input Types</h3>
                <div class="code-block">
                    <pre><code>&lt;form&gt;
    &lt;label for="text-input"&gt;Text:&lt;/label&gt;
    &lt;input type="text" id="text-input" placeholder="Enter text"&gt;

    &lt;label for="password-input"&gt;Password:&lt;/label&gt;
    &lt;input type="password" id="password-input"&gt;

    &lt;label for="email-input"&gt;Email:&lt;/label&gt;
    &lt;input type="email" id="email-input"&gt;

    &lt;label for="number-input"&gt;Number:&lt;/label&gt;
    &lt;input type="number" id="number-input" min="1" max="100"&gt;

    &lt;label for="date-input"&gt;Date:&lt;/label&gt;
    &lt;input type="date" id="date-input"&gt;
&lt;/form&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <form style="display: grid; grid-template-columns: 100px 1fr; gap: 10px; align-items: center; max-width: 400px;">
                        <label for="demo-text">Text:</label>
                        <input type="text" id="demo-text" placeholder="Enter text" style="padding: 8px; border: 1px solid #ccc;">

                        <label for="demo-password">Password:</label>
                        <input type="password" id="demo-password" style="padding: 8px; border: 1px solid #ccc;">

                        <label for="demo-email2">Email:</label>
                        <input type="email" id="demo-email2" style="padding: 8px; border: 1px solid #ccc;">

                        <label for="demo-number">Number:</label>
                        <input type="number" id="demo-number" min="1" max="100" style="padding: 8px; border: 1px solid #ccc;">

                        <label for="demo-date">Date:</label>
                        <input type="date" id="demo-date" style="padding: 8px; border: 1px solid #ccc;">
                    </form>
                </div>
            </div>

            <!-- Example 19: Checkboxes and Radio Buttons -->
            <div class="demo-container">
                <h3>Example 19: Checkboxes and Radio Buttons</h3>
                <div class="code-block">
                    <pre><code>&lt;form&gt;
    &lt;fieldset&gt;
        &lt;legend&gt;Choose your favorite colors:&lt;/legend&gt;
        &lt;input type="checkbox" id="red" name="colors" value="red"&gt;
        &lt;label for="red"&gt;Red&lt;/label&gt;

        &lt;input type="checkbox" id="blue" name="colors" value="blue"&gt;
        &lt;label for="blue"&gt;Blue&lt;/label&gt;

        &lt;input type="checkbox" id="green" name="colors" value="green"&gt;
        &lt;label for="green"&gt;Green&lt;/label&gt;
    &lt;/fieldset&gt;

    &lt;fieldset&gt;
        &lt;legend&gt;Choose your size:&lt;/legend&gt;
        &lt;input type="radio" id="small" name="size" value="small"&gt;
        &lt;label for="small"&gt;Small&lt;/label&gt;

        &lt;input type="radio" id="medium" name="size" value="medium"&gt;
        &lt;label for="medium"&gt;Medium&lt;/label&gt;

        &lt;input type="radio" id="large" name="size" value="large"&gt;
        &lt;label for="large"&gt;Large&lt;/label&gt;
    &lt;/fieldset&gt;
&lt;/form&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <form>
                        <fieldset style="border: 1px solid #ccc; padding: 10px; margin: 10px 0;">
                            <legend>Choose your favorite colors:</legend>
                            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                                <label><input type="checkbox" name="colors" value="red"> Red</label>
                                <label><input type="checkbox" name="colors" value="blue"> Blue</label>
                                <label><input type="checkbox" name="colors" value="green"> Green</label>
                            </div>
                        </fieldset>

                        <fieldset style="border: 1px solid #ccc; padding: 10px; margin: 10px 0;">
                            <legend>Choose your size:</legend>
                            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                                <label><input type="radio" name="size" value="small"> Small</label>
                                <label><input type="radio" name="size" value="medium"> Medium</label>
                                <label><input type="radio" name="size" value="large"> Large</label>
                            </div>
                        </fieldset>
                    </form>
                </div>
                <p class="explanation">✅ Radio buttons with the same 'name' are mutually exclusive. Checkboxes allow multiple selections.</p>
            </div>

            <!-- Example 20: Select Dropdown -->
            <div class="demo-container">
                <h3>Example 20: Dropdown Select</h3>
                <div class="code-block">
                    <pre><code>&lt;form&gt;
    &lt;label for="country"&gt;Choose your country:&lt;/label&gt;
    &lt;select id="country" name="country"&gt;
        &lt;option value=""&gt;Select a country&lt;/option&gt;
        &lt;option value="us"&gt;United States&lt;/option&gt;
        &lt;option value="ca"&gt;Canada&lt;/option&gt;
        &lt;option value="uk"&gt;United Kingdom&lt;/option&gt;
        &lt;option value="de"&gt;Germany&lt;/option&gt;
    &lt;/select&gt;
&lt;/form&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <form style="display: flex; flex-direction: column; gap: 10px; max-width: 300px;">
                        <label for="demo-country">Choose your country:</label>
                        <select id="demo-country" name="country" style="padding: 8px; border: 1px solid #ccc;">
                            <option value="">Select a country</option>
                            <option value="us">United States</option>
                            <option value="ca">Canada</option>
                            <option value="uk">United Kingdom</option>
                            <option value="de">Germany</option>
                        </select>
                    </form>
                </div>
            </div>
        </section>

        <section class="examples" id="semantic-html">
            <h2>Building Up to Semantic HTML5</h2>
            <p>Now let's learn HTML5 semantic elements and see how everything comes together in real websites.</p>

            <!-- Example 21: Textarea -->
            <div class="demo-container">
                <h3>Example 21: Textarea for Long Text</h3>
                <div class="code-block">
                    <pre><code>&lt;form&gt;
    &lt;label for="message"&gt;Your Message:&lt;/label&gt;
    &lt;textarea id="message" name="message" rows="4" cols="50" placeholder="Enter your message here..."&gt;&lt;/textarea&gt;
&lt;/form&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <form style="display: flex; flex-direction: column; gap: 10px; max-width: 400px;">
                        <label for="demo-message">Your Message:</label>
                        <textarea id="demo-message" name="message" rows="4" placeholder="Enter your message here..." style="padding: 8px; border: 1px solid #ccc; resize: vertical;"></textarea>
                    </form>
                </div>
            </div>

            <!-- Example 22: Div and Span -->
            <div class="demo-container">
                <h3>Example 22: Generic Containers (div and span)</h3>
                <div class="code-block">
                    <pre><code>&lt;div&gt;
    &lt;h3&gt;This is a section&lt;/h3&gt;
    &lt;p&gt;This paragraph contains &lt;span&gt;highlighted text&lt;/span&gt; in the middle.&lt;/p&gt;
&lt;/div&gt;

&lt;div&gt;
    &lt;h3&gt;Another section&lt;/h3&gt;
    &lt;p&gt;Div creates block-level containers, span creates inline containers.&lt;/p&gt;
&lt;/div&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; background: #f9f9f9;">
                        <h3>This is a section</h3>
                        <p>This paragraph contains <span style="background: yellow; padding: 2px;">highlighted text</span> in the middle.</p>
                    </div>

                    <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; background: #f9f9f9;">
                        <h3>Another section</h3>
                        <p>Div creates block-level containers, span creates inline containers.</p>
                    </div>
                </div>
                <p class="explanation">✅ Use div and span when no semantic element fits. Prefer semantic elements when possible.</p>
            </div>

            <!-- Example 23: HTML5 Semantic Header -->
            <div class="demo-container">
                <h3>Example 23: Semantic Header Element</h3>
                <div class="code-block">
                    <pre><code>&lt;header&gt;
    &lt;h1&gt;My Website&lt;/h1&gt;
    &lt;p&gt;Welcome to my amazing website&lt;/p&gt;
&lt;/header&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <header style="background: #e3f2fd; padding: 20px; border-radius: 8px;">
                        <h1 style="margin: 0 0 10px 0; color: #1976d2;">My Website</h1>
                        <p style="margin: 0; color: #666;">Welcome to my amazing website</p>
                    </header>
                </div>
                <p class="explanation">✅ &lt;header&gt; represents introductory content for a page or section.</p>
            </div>

            <!-- Example 24: Navigation Element -->
            <div class="demo-container">
                <h3>Example 24: Semantic Navigation</h3>
                <div class="code-block">
                    <pre><code>&lt;nav&gt;
    &lt;ul&gt;
        &lt;li&gt;&lt;a href="#home"&gt;Home&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="#about"&gt;About&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="#services"&gt;Services&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="#contact"&gt;Contact&lt;/a&gt;&lt;/li&gt;
    &lt;/ul&gt;
&lt;/nav&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <nav style="background: #f5f5f5; padding: 15px; border-radius: 8px;">
                        <ul style="list-style: none; padding: 0; margin: 0; display: flex; gap: 20px;">
                            <li><a href="#home" style="color: #007bff; text-decoration: none; padding: 8px 12px; border-radius: 4px; background: white;">Home</a></li>
                            <li><a href="#about" style="color: #007bff; text-decoration: none; padding: 8px 12px; border-radius: 4px; background: white;">About</a></li>
                            <li><a href="#services" style="color: #007bff; text-decoration: none; padding: 8px 12px; border-radius: 4px; background: white;">Services</a></li>
                            <li><a href="#contact" style="color: #007bff; text-decoration: none; padding: 8px 12px; border-radius: 4px; background: white;">Contact</a></li>
                        </ul>
                    </nav>
                </div>
                <p class="explanation">✅ &lt;nav&gt; represents navigation links for the site or page.</p>
            </div>

            <!-- Example 25: Main Content Area -->
            <div class="demo-container">
                <h3>Example 25: Main Content Element</h3>
                <div class="code-block">
                    <pre><code>&lt;main&gt;
    &lt;h1&gt;Welcome to Our Blog&lt;/h1&gt;
    &lt;p&gt;This is the main content area of the page.&lt;/p&gt;
    &lt;p&gt;Here you'll find our latest articles and updates.&lt;/p&gt;
&lt;/main&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <main style="background: #fff; padding: 20px; border: 2px solid #28a745; border-radius: 8px;">
                        <h1 style="color: #28a745; margin-top: 0;">Welcome to Our Blog</h1>
                        <p>This is the main content area of the page.</p>
                        <p>Here you'll find our latest articles and updates.</p>
                    </main>
                </div>
                <p class="explanation">✅ &lt;main&gt; represents the main content of the page. Use only once per page.</p>
            </div>

            <!-- Example 26: Article Element -->
            <div class="demo-container">
                <h3>Example 26: Article Element</h3>
                <div class="code-block">
                    <pre><code>&lt;article&gt;
    &lt;header&gt;
        &lt;h2&gt;How to Learn HTML&lt;/h2&gt;
        &lt;p&gt;Published on &lt;time datetime="2024-01-15"&gt;January 15, 2024&lt;/time&gt;&lt;/p&gt;
    &lt;/header&gt;

    &lt;p&gt;Learning HTML is the first step in web development...&lt;/p&gt;
    &lt;p&gt;Start with basic elements and gradually build complexity.&lt;/p&gt;

    &lt;footer&gt;
        &lt;p&gt;Tags: HTML, Web Development, Tutorial&lt;/p&gt;
    &lt;/footer&gt;
&lt;/article&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <article style="background: #f8f9fa; padding: 20px; border-left: 4px solid #6f42c1; margin: 10px 0;">
                        <header style="margin-bottom: 15px;">
                            <h2 style="color: #6f42c1; margin: 0 0 5px 0;">How to Learn HTML</h2>
                            <p style="color: #666; margin: 0; font-size: 0.9em;">Published on <time datetime="2024-01-15">January 15, 2024</time></p>
                        </header>

                        <p>Learning HTML is the first step in web development...</p>
                        <p>Start with basic elements and gradually build complexity.</p>

                        <footer style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #ddd;">
                            <p style="color: #666; font-size: 0.9em; margin: 0;">Tags: HTML, Web Development, Tutorial</p>
                        </footer>
                    </article>
                </div>
                <p class="explanation">✅ &lt;article&gt; represents standalone content that could be distributed independently.</p>
            </div>

            <!-- Example 27: Section Element -->
            <div class="demo-container">
                <h3>Example 27: Section Element</h3>
                <div class="code-block">
                    <pre><code>&lt;section&gt;
    &lt;h2&gt;Our Services&lt;/h2&gt;
    &lt;p&gt;We offer a variety of web development services.&lt;/p&gt;

    &lt;section&gt;
        &lt;h3&gt;Frontend Development&lt;/h3&gt;
        &lt;p&gt;HTML, CSS, and JavaScript development.&lt;/p&gt;
    &lt;/section&gt;

    &lt;section&gt;
        &lt;h3&gt;Backend Development&lt;/h3&gt;
        &lt;p&gt;Server-side programming and databases.&lt;/p&gt;
    &lt;/section&gt;
&lt;/section&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <section style="background: #e8f5e8; padding: 20px; border-radius: 8px;">
                        <h2 style="color: #28a745; margin-top: 0;">Our Services</h2>
                        <p>We offer a variety of web development services.</p>

                        <section style="background: white; padding: 15px; margin: 10px 0; border-radius: 4px;">
                            <h3 style="color: #28a745; margin-top: 0;">Frontend Development</h3>
                            <p>HTML, CSS, and JavaScript development.</p>
                        </section>

                        <section style="background: white; padding: 15px; margin: 10px 0; border-radius: 4px;">
                            <h3 style="color: #28a745; margin-top: 0;">Backend Development</h3>
                            <p>Server-side programming and databases.</p>
                        </section>
                    </section>
                </div>
                <p class="explanation">✅ &lt;section&gt; represents a thematic grouping of content with a heading.</p>
            </div>

            <!-- Example 28: Aside Element -->
            <div class="demo-container">
                <h3>Example 28: Aside Element (Sidebar)</h3>
                <div class="code-block">
                    <pre><code>&lt;aside&gt;
    &lt;h3&gt;Related Articles&lt;/h3&gt;
    &lt;ul&gt;
        &lt;li&gt;&lt;a href="#"&gt;CSS Basics&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="#"&gt;JavaScript Fundamentals&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="#"&gt;Responsive Design&lt;/a&gt;&lt;/li&gt;
    &lt;/ul&gt;

    &lt;h3&gt;Advertisement&lt;/h3&gt;
    &lt;p&gt;Learn web development with our online courses!&lt;/p&gt;
&lt;/aside&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <aside style="background: #fff3cd; padding: 20px; border-left: 4px solid #ffc107; border-radius: 8px;">
                        <h3 style="color: #856404; margin-top: 0;">Related Articles</h3>
                        <ul style="color: #856404;">
                            <li><a href="#" style="color: #856404;">CSS Basics</a></li>
                            <li><a href="#" style="color: #856404;">JavaScript Fundamentals</a></li>
                            <li><a href="#" style="color: #856404;">Responsive Design</a></li>
                        </ul>

                        <h3 style="color: #856404;">Advertisement</h3>
                        <p style="color: #856404;">Learn web development with our online courses!</p>
                    </aside>
                </div>
                <p class="explanation">✅ &lt;aside&gt; represents content tangentially related to the main content.</p>
            </div>

            <!-- Example 29: Footer Element -->
            <div class="demo-container">
                <h3>Example 29: Footer Element</h3>
                <div class="code-block">
                    <pre><code>&lt;footer&gt;
    &lt;p&gt;&copy; 2024 My Website. All rights reserved.&lt;/p&gt;
    &lt;nav&gt;
        &lt;a href="#privacy"&gt;Privacy Policy&lt;/a&gt; |
        &lt;a href="#terms"&gt;Terms of Service&lt;/a&gt; |
        &lt;a href="#contact"&gt;Contact Us&lt;/a&gt;
    &lt;/nav&gt;
&lt;/footer&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <footer style="background: #343a40; color: white; padding: 20px; text-align: center; border-radius: 8px;">
                        <p>&copy; 2024 My Website. All rights reserved.</p>
                        <nav>
                            <a href="#privacy" style="color: #adb5bd; text-decoration: none;">Privacy Policy</a> |
                            <a href="#terms" style="color: #adb5bd; text-decoration: none; margin: 0 5px;">Terms of Service</a> |
                            <a href="#contact" style="color: #adb5bd; text-decoration: none;">Contact Us</a>
                        </nav>
                    </footer>
                </div>
                <p class="explanation">✅ &lt;footer&gt; represents footer information for a page or section.</p>
            </div>

            <!-- Example 30: Figure and Figcaption -->
            <div class="demo-container">
                <h3>Example 30: Figure with Caption</h3>
                <div class="code-block">
                    <pre><code>&lt;figure&gt;
    &lt;img src="chart.png" alt="Sales growth chart"&gt;
    &lt;figcaption&gt;Figure 1: Sales growth over the last 5 years&lt;/figcaption&gt;
&lt;/figure&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <figure style="margin: 0; text-align: center; background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <img src="https://via.placeholder.com/300x200/17a2b8/ffffff?text=Sales+Chart" alt="Sales growth chart" style="max-width: 100%; height: auto; border-radius: 4px;">
                        <figcaption style="margin-top: 10px; font-style: italic; color: #666;">Figure 1: Sales growth over the last 5 years</figcaption>
                    </figure>
                </div>
                <p class="explanation">✅ &lt;figure&gt; represents self-contained content with an optional caption.</p>
            </div>
        </section>

        <section class="advanced" id="forms">
            <h2>Advanced HTML5 Forms & Real-World Examples</h2>
            <p>Now let's build complex forms and complete web pages using everything we've learned, showing how it all comes together.</p>

            <!-- Example 31: Advanced Form with Validation -->
            <div class="demo-container">
                <h3>Example 31: Complete Contact Form</h3>
                <div class="code-block">
                    <pre><code>&lt;form action="/submit" method="post"&gt;
    &lt;fieldset&gt;
        &lt;legend&gt;Contact Information&lt;/legend&gt;

        &lt;label for="fullname"&gt;Full Name *&lt;/label&gt;
        &lt;input type="text" id="fullname" name="fullname" required&gt;

        &lt;label for="email"&gt;Email Address *&lt;/label&gt;
        &lt;input type="email" id="email" name="email" required&gt;

        &lt;label for="phone"&gt;Phone Number&lt;/label&gt;
        &lt;input type="tel" id="phone" name="phone" pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" placeholder="************"&gt;
    &lt;/fieldset&gt;

    &lt;fieldset&gt;
        &lt;legend&gt;Message Details&lt;/legend&gt;

        &lt;label for="subject"&gt;Subject&lt;/label&gt;
        &lt;select id="subject" name="subject"&gt;
            &lt;option value=""&gt;Choose a subject&lt;/option&gt;
            &lt;option value="general"&gt;General Inquiry&lt;/option&gt;
            &lt;option value="support"&gt;Technical Support&lt;/option&gt;
            &lt;option value="sales"&gt;Sales Question&lt;/option&gt;
        &lt;/select&gt;

        &lt;label for="message"&gt;Your Message *&lt;/label&gt;
        &lt;textarea id="message" name="message" rows="5" required placeholder="Please enter your message here..."&gt;&lt;/textarea&gt;
    &lt;/fieldset&gt;

    &lt;button type="submit"&gt;Send Message&lt;/button&gt;
    &lt;button type="reset"&gt;Clear Form&lt;/button&gt;
&lt;/form&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <form style="max-width: 500px; margin: 0 auto;">
                        <fieldset style="border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 8px;">
                            <legend style="padding: 0 10px; font-weight: bold;">Contact Information</legend>

                            <div style="margin: 10px 0;">
                                <label for="demo-fullname" style="display: block; margin-bottom: 5px;">Full Name *</label>
                                <input type="text" id="demo-fullname" name="fullname" required style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                            </div>

                            <div style="margin: 10px 0;">
                                <label for="demo-email3" style="display: block; margin-bottom: 5px;">Email Address *</label>
                                <input type="email" id="demo-email3" name="email" required style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                            </div>

                            <div style="margin: 10px 0;">
                                <label for="demo-phone2" style="display: block; margin-bottom: 5px;">Phone Number</label>
                                <input type="tel" id="demo-phone2" name="phone" pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" placeholder="************" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                            </div>
                        </fieldset>

                        <fieldset style="border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 8px;">
                            <legend style="padding: 0 10px; font-weight: bold;">Message Details</legend>

                            <div style="margin: 10px 0;">
                                <label for="demo-subject" style="display: block; margin-bottom: 5px;">Subject</label>
                                <select id="demo-subject" name="subject" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                                    <option value="">Choose a subject</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="support">Technical Support</option>
                                    <option value="sales">Sales Question</option>
                                </select>
                            </div>

                            <div style="margin: 10px 0;">
                                <label for="demo-message2" style="display: block; margin-bottom: 5px;">Your Message *</label>
                                <textarea id="demo-message2" name="message" rows="5" required placeholder="Please enter your message here..." style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; resize: vertical;"></textarea>
                            </div>
                        </fieldset>

                        <div style="text-align: center; margin: 20px 0;">
                            <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin: 0 5px; cursor: pointer;">Send Message</button>
                            <button type="reset" style="background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin: 0 5px; cursor: pointer;">Clear Form</button>
                        </div>
                    </form>
                </div>
                <p class="explanation">✅ This form combines multiple input types, validation, and proper structure.</p>
            </div>

            <!-- Example 32: HTML5 Media Elements -->
            <div class="demo-container">
                <h3>Example 32: Audio and Video Elements</h3>
                <div class="code-block">
                    <pre><code>&lt;!-- Audio Element --&gt;
&lt;audio controls&gt;
    &lt;source src="audio.mp3" type="audio/mpeg"&gt;
    &lt;source src="audio.ogg" type="audio/ogg"&gt;
    Your browser does not support the audio element.
&lt;/audio&gt;

&lt;!-- Video Element --&gt;
&lt;video controls width="400" height="300"&gt;
    &lt;source src="video.mp4" type="video/mp4"&gt;
    &lt;source src="video.webm" type="video/webm"&gt;
    &lt;track kind="subtitles" src="subtitles.vtt" srclang="en" label="English"&gt;
    Your browser does not support the video tag.
&lt;/video&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <p><strong>Audio Player:</strong></p>
                        <audio controls style="margin: 10px;">
                            <source src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" type="audio/wav">
                            Your browser does not support the audio element.
                        </audio>

                        <p><strong>Video Player:</strong></p>
                        <video controls width="300" height="200" style="border-radius: 8px;">
                            <source src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                </div>
                <p class="explanation">✅ Always provide multiple formats and fallback content for better browser support.</p>
            </div>

            <!-- Example 33: Canvas Element -->
            <div class="demo-container">
                <h3>Example 33: Canvas for Graphics</h3>
                <div class="code-block">
                    <pre><code>&lt;canvas id="myCanvas" width="400" height="200"&gt;
    Your browser does not support the canvas element.
&lt;/canvas&gt;

&lt;script&gt;
const canvas = document.getElementById('myCanvas');
const ctx = canvas.getContext('2d');

// Draw a rectangle
ctx.fillStyle = '#3498db';
ctx.fillRect(50, 50, 100, 80);

// Draw a circle
ctx.beginPath();
ctx.arc(250, 90, 40, 0, 2 * Math.PI);
ctx.fillStyle = '#e74c3c';
ctx.fill();
&lt;/script&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <canvas id="demo-canvas" width="400" height="200" style="border: 1px solid #ddd; border-radius: 8px; background: white;"></canvas>
                    <div style="margin-top: 10px; text-align: center;">
                        <button onclick="drawShapes()" style="background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">Draw Shapes</button>
                        <button onclick="clearCanvas()" style="background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">Clear</button>
                    </div>
                </div>
            </div>

            <!-- Example 34: Complete Blog Post Structure -->
            <div class="demo-container">
                <h3>Example 34: Complete Blog Post (Putting It All Together)</h3>
                <div class="code-block">
                    <pre><code>&lt;article&gt;
    &lt;header&gt;
        &lt;h1&gt;The Complete Guide to HTML5&lt;/h1&gt;
        &lt;p&gt;By &lt;strong&gt;John Doe&lt;/strong&gt; on &lt;time datetime="2024-01-15"&gt;January 15, 2024&lt;/time&gt;&lt;/p&gt;
        &lt;p&gt;Reading time: 5 minutes&lt;/p&gt;
    &lt;/header&gt;

    &lt;figure&gt;
        &lt;img src="html5-guide.jpg" alt="HTML5 logo and code examples"&gt;
        &lt;figcaption&gt;HTML5 brings semantic meaning to web development&lt;/figcaption&gt;
    &lt;/figure&gt;

    &lt;section&gt;
        &lt;h2&gt;Introduction&lt;/h2&gt;
        &lt;p&gt;HTML5 revolutionized web development by introducing &lt;em&gt;semantic elements&lt;/em&gt; that give meaning to content structure.&lt;/p&gt;

        &lt;blockquote&gt;
            &lt;p&gt;"HTML5 is not just about new tags, it's about creating meaningful, accessible web content."&lt;/p&gt;
            &lt;cite&gt;— Web Standards Expert&lt;/cite&gt;
        &lt;/blockquote&gt;
    &lt;/section&gt;

    &lt;section&gt;
        &lt;h2&gt;Key Benefits&lt;/h2&gt;
        &lt;ul&gt;
            &lt;li&gt;&lt;strong&gt;Better SEO:&lt;/strong&gt; Search engines understand content structure&lt;/li&gt;
            &lt;li&gt;&lt;strong&gt;Accessibility:&lt;/strong&gt; Screen readers navigate content easily&lt;/li&gt;
            &lt;li&gt;&lt;strong&gt;Maintainability:&lt;/strong&gt; Code is more readable and organized&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/section&gt;

    &lt;footer&gt;
        &lt;p&gt;Tags: &lt;a href="#html5"&gt;HTML5&lt;/a&gt;, &lt;a href="#webdev"&gt;Web Development&lt;/a&gt;, &lt;a href="#semantic"&gt;Semantic HTML&lt;/a&gt;&lt;/p&gt;
        &lt;p&gt;Share this article:
            &lt;a href="#twitter"&gt;Twitter&lt;/a&gt; |
            &lt;a href="#facebook"&gt;Facebook&lt;/a&gt; |
            &lt;a href="#linkedin"&gt;LinkedIn&lt;/a&gt;
        &lt;/p&gt;
    &lt;/footer&gt;
&lt;/article&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <article style="max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                        <header style="margin-bottom: 30px; text-align: center; border-bottom: 2px solid #e9ecef; padding-bottom: 20px;">
                            <h1 style="color: #2c3e50; margin: 0 0 15px 0; font-size: 2.2em;">The Complete Guide to HTML5</h1>
                            <p style="color: #6c757d; margin: 5px 0;">By <strong>John Doe</strong> on <time datetime="2024-01-15">January 15, 2024</time></p>
                            <p style="color: #6c757d; margin: 5px 0; font-size: 0.9em;">Reading time: 5 minutes</p>
                        </header>

                        <figure style="margin: 30px 0; text-align: center;">
                            <img src="https://via.placeholder.com/500x250/e74c3c/ffffff?text=HTML5+Guide" alt="HTML5 logo and code examples" style="max-width: 100%; height: auto; border-radius: 8px;">
                            <figcaption style="margin-top: 10px; font-style: italic; color: #6c757d; font-size: 0.9em;">HTML5 brings semantic meaning to web development</figcaption>
                        </figure>

                        <section style="margin: 30px 0;">
                            <h2 style="color: #2c3e50; border-bottom: 1px solid #e9ecef; padding-bottom: 10px;">Introduction</h2>
                            <p style="line-height: 1.6; color: #495057;">HTML5 revolutionized web development by introducing <em>semantic elements</em> that give meaning to content structure.</p>

                            <blockquote style="border-left: 4px solid #007bff; padding-left: 20px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px 20px; border-radius: 0 8px 8px 0;">
                                <p style="margin: 0 0 10px 0;">"HTML5 is not just about new tags, it's about creating meaningful, accessible web content."</p>
                                <cite style="font-size: 0.9em; color: #6c757d;">— Web Standards Expert</cite>
                            </blockquote>
                        </section>

                        <section style="margin: 30px 0;">
                            <h2 style="color: #2c3e50; border-bottom: 1px solid #e9ecef; padding-bottom: 10px;">Key Benefits</h2>
                            <ul style="line-height: 1.8; color: #495057;">
                                <li><strong>Better SEO:</strong> Search engines understand content structure</li>
                                <li><strong>Accessibility:</strong> Screen readers navigate content easily</li>
                                <li><strong>Maintainability:</strong> Code is more readable and organized</li>
                            </ul>
                        </section>

                        <footer style="margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef; font-size: 0.9em; color: #6c757d;">
                            <p>Tags: <a href="#html5" style="color: #007bff; text-decoration: none;">HTML5</a>, <a href="#webdev" style="color: #007bff; text-decoration: none;">Web Development</a>, <a href="#semantic" style="color: #007bff; text-decoration: none;">Semantic HTML</a></p>
                            <p>Share this article:
                                <a href="#twitter" style="color: #007bff; text-decoration: none;">Twitter</a> |
                                <a href="#facebook" style="color: #007bff; text-decoration: none;">Facebook</a> |
                                <a href="#linkedin" style="color: #007bff; text-decoration: none;">LinkedIn</a>
                            </p>
                        </footer>
                    </article>
                </div>
                <p class="explanation">✅ This example shows how semantic HTML creates meaningful, well-structured content.</p>
            </div>

            <!-- Example 35: Complete Website Layout -->
            <div class="demo-container">
                <h3>Example 35: Complete Website Structure (The Big Picture)</h3>
                <div class="code-block">
                    <pre><code>&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;My Professional Website&lt;/title&gt;
    &lt;meta name="description" content="Professional web developer specializing in modern HTML5, CSS3, and JavaScript"&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;header&gt;
        &lt;h1&gt;John Developer&lt;/h1&gt;
        &lt;nav&gt;
            &lt;ul&gt;
                &lt;li&gt;&lt;a href="#home"&gt;Home&lt;/a&gt;&lt;/li&gt;
                &lt;li&gt;&lt;a href="#about"&gt;About&lt;/a&gt;&lt;/li&gt;
                &lt;li&gt;&lt;a href="#services"&gt;Services&lt;/a&gt;&lt;/li&gt;
                &lt;li&gt;&lt;a href="#portfolio"&gt;Portfolio&lt;/a&gt;&lt;/li&gt;
                &lt;li&gt;&lt;a href="#contact"&gt;Contact&lt;/a&gt;&lt;/li&gt;
            &lt;/ul&gt;
        &lt;/nav&gt;
    &lt;/header&gt;

    &lt;main&gt;
        &lt;section id="home"&gt;
            &lt;h2&gt;Welcome to My Website&lt;/h2&gt;
            &lt;p&gt;I create modern, responsive websites using the latest web technologies.&lt;/p&gt;
        &lt;/section&gt;

        &lt;section id="about"&gt;
            &lt;h2&gt;About Me&lt;/h2&gt;
            &lt;p&gt;I'm a passionate web developer with 5 years of experience...&lt;/p&gt;
        &lt;/section&gt;

        &lt;section id="services"&gt;
            &lt;h2&gt;My Services&lt;/h2&gt;
            &lt;article&gt;
                &lt;h3&gt;Frontend Development&lt;/h3&gt;
                &lt;p&gt;HTML5, CSS3, JavaScript, React&lt;/p&gt;
            &lt;/article&gt;
            &lt;article&gt;
                &lt;h3&gt;Backend Development&lt;/h3&gt;
                &lt;p&gt;Node.js, Python, Database Design&lt;/p&gt;
            &lt;/article&gt;
        &lt;/section&gt;
    &lt;/main&gt;

    &lt;aside&gt;
        &lt;h3&gt;Latest Blog Posts&lt;/h3&gt;
        &lt;ul&gt;
            &lt;li&gt;&lt;a href="#"&gt;HTML5 Best Practices&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a href="#"&gt;CSS Grid vs Flexbox&lt;/a&gt;&lt;/li&gt;
            &lt;li&gt;&lt;a href="#"&gt;JavaScript ES2024 Features&lt;/a&gt;&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/aside&gt;

    &lt;footer&gt;
        &lt;p&gt;&copy; 2024 John Developer. All rights reserved.&lt;/p&gt;
        &lt;nav&gt;
            &lt;a href="#privacy"&gt;Privacy&lt;/a&gt; |
            &lt;a href="#terms"&gt;Terms&lt;/a&gt; |
            &lt;a href="mailto:<EMAIL>"&gt;Email Me&lt;/a&gt;
        &lt;/nav&gt;
    &lt;/footer&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <div style="max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif; border: 1px solid #ddd; border-radius: 12px; overflow: hidden;">
                        <!-- Header -->
                        <header style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center;">
                            <h1 style="margin: 0 0 15px 0; font-size: 2.5em;">John Developer</h1>
                            <nav>
                                <ul style="list-style: none; padding: 0; margin: 0; display: flex; justify-content: center; gap: 30px; flex-wrap: wrap;">
                                    <li><a href="#home" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 20px; background: rgba(255,255,255,0.2);">Home</a></li>
                                    <li><a href="#about" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 20px; background: rgba(255,255,255,0.2);">About</a></li>
                                    <li><a href="#services" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 20px; background: rgba(255,255,255,0.2);">Services</a></li>
                                    <li><a href="#portfolio" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 20px; background: rgba(255,255,255,0.2);">Portfolio</a></li>
                                    <li><a href="#contact" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 20px; background: rgba(255,255,255,0.2);">Contact</a></li>
                                </ul>
                            </nav>
                        </header>

                        <div style="display: flex; min-height: 400px;">
                            <!-- Main Content -->
                            <main style="flex: 2; padding: 30px;">
                                <section id="home" style="margin-bottom: 30px;">
                                    <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">Welcome to My Website</h2>
                                    <p style="line-height: 1.6; color: #555;">I create modern, responsive websites using the latest web technologies.</p>
                                </section>

                                <section id="about" style="margin-bottom: 30px;">
                                    <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">About Me</h2>
                                    <p style="line-height: 1.6; color: #555;">I'm a passionate web developer with 5 years of experience creating beautiful, functional websites.</p>
                                </section>

                                <section id="services">
                                    <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">My Services</h2>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
                                        <article style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
                                            <h3 style="color: #28a745; margin-top: 0;">Frontend Development</h3>
                                            <p style="color: #555; margin-bottom: 0;">HTML5, CSS3, JavaScript, React</p>
                                        </article>
                                        <article style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #dc3545;">
                                            <h3 style="color: #dc3545; margin-top: 0;">Backend Development</h3>
                                            <p style="color: #555; margin-bottom: 0;">Node.js, Python, Database Design</p>
                                        </article>
                                    </div>
                                </section>
                            </main>

                            <!-- Sidebar -->
                            <aside style="flex: 1; background: #f8f9fa; padding: 30px; border-left: 1px solid #e9ecef;">
                                <h3 style="color: #2c3e50; margin-top: 0;">Latest Blog Posts</h3>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="margin: 15px 0;"><a href="#" style="color: #007bff; text-decoration: none; display: block; padding: 10px; background: white; border-radius: 4px; border-left: 3px solid #007bff;">HTML5 Best Practices</a></li>
                                    <li style="margin: 15px 0;"><a href="#" style="color: #007bff; text-decoration: none; display: block; padding: 10px; background: white; border-radius: 4px; border-left: 3px solid #007bff;">CSS Grid vs Flexbox</a></li>
                                    <li style="margin: 15px 0;"><a href="#" style="color: #007bff; text-decoration: none; display: block; padding: 10px; background: white; border-radius: 4px; border-left: 3px solid #007bff;">JavaScript ES2024 Features</a></li>
                                </ul>
                            </aside>
                        </div>

                        <!-- Footer -->
                        <footer style="background: #2c3e50; color: white; padding: 20px; text-align: center;">
                            <p style="margin: 0 0 10px 0;">&copy; 2024 John Developer. All rights reserved.</p>
                            <nav>
                                <a href="#privacy" style="color: #bdc3c7; text-decoration: none; margin: 0 10px;">Privacy</a> |
                                <a href="#terms" style="color: #bdc3c7; text-decoration: none; margin: 0 10px;">Terms</a> |
                                <a href="mailto:<EMAIL>" style="color: #bdc3c7; text-decoration: none; margin: 0 10px;">Email Me</a>
                            </nav>
                        </footer>
                    </div>
                </div>
                <p class="explanation">🎉 <strong>Congratulations!</strong> This is a complete, semantic HTML5 website structure using everything we've learned!</p>
            </div>
        </section>
            
            <div class="demo-container">
                <h3>Modern Form Elements</h3>
                <div class="live-demo">
                    <form class="modern-form" id="html5-form">
                        <fieldset>
                            <legend>Personal Information</legend>
                            
                            <div class="form-group">
                                <label for="name">Full Name *</label>
                                <input type="text" id="name" name="name" required placeholder="Enter your full name">
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                            </div>
                            
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone" placeholder="+****************">
                            </div>
                            
                            <div class="form-group">
                                <label for="birthdate">Date of Birth</label>
                                <input type="date" id="birthdate" name="birthdate">
                            </div>
                            
                            <div class="form-group">
                                <label for="website">Website</label>
                                <input type="url" id="website" name="website" placeholder="https://example.com">
                            </div>
                            
                            <div class="form-group">
                                <label for="age">Age</label>
                                <input type="number" id="age" name="age" min="18" max="120" placeholder="25">
                            </div>
                            
                            <div class="form-group">
                                <label for="experience">Experience Level</label>
                                <input type="range" id="experience" name="experience" min="0" max="10" value="5">
                                <output for="experience">5</output>
                            </div>
                            
                            <div class="form-group">
                                <label for="color-preference">Favorite Color</label>
                                <input type="color" id="color-preference" name="color-preference" value="#3498db">
                            </div>
                        </fieldset>
                        
                        <fieldset>
                            <legend>Preferences</legend>
                            
                            <div class="form-group">
                                <label for="bio">Bio</label>
                                <textarea id="bio" name="bio" rows="4" placeholder="Tell us about yourself..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="country">Country</label>
                                <select id="country" name="country">
                                    <option value="">Select a country</option>
                                    <option value="us">United States</option>
                                    <option value="ca">Canada</option>
                                    <option value="uk">United Kingdom</option>
                                    <option value="de">Germany</option>
                                    <option value="fr">France</option>
                                </select>
                            </div>
                            
                            <div class="form-group checkbox-group">
                                <input type="checkbox" id="newsletter" name="newsletter">
                                <label for="newsletter">Subscribe to newsletter</label>
                            </div>
                            
                            <div class="form-group radio-group">
                                <fieldset>
                                    <legend>Preferred Contact Method</legend>
                                    <input type="radio" id="contact-email" name="contact-method" value="email">
                                    <label for="contact-email">Email</label>
                                    
                                    <input type="radio" id="contact-phone" name="contact-method" value="phone">
                                    <label for="contact-phone">Phone</label>
                                    
                                    <input type="radio" id="contact-mail" name="contact-method" value="mail">
                                    <label for="contact-mail">Mail</label>
                                </fieldset>
                            </div>
                        </fieldset>
                        
                        <div class="form-actions">
                            <button type="submit">Submit Form</button>
                            <button type="reset">Reset Form</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <section class="advanced" id="media">
            <h2>HTML5 Media Elements</h2>
            <p>HTML5 provides native support for audio, video, and graphics through various media elements.</p>
            
            <div class="demo-container">
                <h3>Video Element</h3>
                <div class="code-block">
                    <pre><code>&lt;video controls width="100%" height="300"&gt;
    &lt;source src="video.mp4" type="video/mp4"&gt;
    &lt;source src="video.webm" type="video/webm"&gt;
    &lt;track kind="subtitles" src="subtitles.vtt" srclang="en" label="English"&gt;
    Your browser does not support the video tag.
&lt;/video&gt;</code></pre>
                </div>
            </div>
            
            <div class="demo-container">
                <h3>Audio Element</h3>
                <div class="code-block">
                    <pre><code>&lt;audio controls&gt;
    &lt;source src="audio.mp3" type="audio/mpeg"&gt;
    &lt;source src="audio.ogg" type="audio/ogg"&gt;
    Your browser does not support the audio element.
&lt;/audio&gt;</code></pre>
                </div>
            </div>
            
            <div class="demo-container">
                <h3>Canvas Element</h3>
                <div class="live-demo">
                    <canvas id="demo-canvas" width="400" height="200" style="border: 1px solid #ccc;"></canvas>
                    <div style="margin-top: 1rem;">
                        <button onclick="drawCircle()">Draw Circle</button>
                        <button onclick="drawRectangle()">Draw Rectangle</button>
                        <button onclick="clearCanvas()">Clear Canvas</button>
                    </div>
                </div>
            </div>
        </section>

        <section class="best-practices" id="accessibility">
            <h2>Accessibility & Best Practices (Examples 36-50+)</h2>
            <p>Let's complete our journey with accessibility, SEO, and professional best practices.</p>

            <!-- Example 36: Accessibility Attributes -->
            <div class="demo-container">
                <h3>Example 36: ARIA Attributes for Accessibility</h3>
                <div class="code-block">
                    <pre><code>&lt;nav aria-label="Main navigation"&gt;
    &lt;ul&gt;
        &lt;li&gt;&lt;a href="#home" aria-current="page"&gt;Home&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="#about"&gt;About&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href="#contact"&gt;Contact&lt;/a&gt;&lt;/li&gt;
    &lt;/ul&gt;
&lt;/nav&gt;

&lt;button aria-expanded="false" aria-controls="menu"&gt;Menu&lt;/button&gt;
&lt;div id="menu" aria-hidden="true"&gt;
    &lt;!-- Menu content --&gt;
&lt;/div&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <nav aria-label="Main navigation" style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <ul style="list-style: none; padding: 0; margin: 0; display: flex; gap: 20px;">
                            <li><a href="#home" aria-current="page" style="color: #007bff; text-decoration: none; padding: 8px 12px; background: #e7f3ff; border-radius: 4px;">Home</a></li>
                            <li><a href="#about" style="color: #007bff; text-decoration: none; padding: 8px 12px;">About</a></li>
                            <li><a href="#contact" style="color: #007bff; text-decoration: none; padding: 8px 12px;">Contact</a></li>
                        </ul>
                    </nav>
                    <div style="margin-top: 15px;">
                        <button aria-expanded="false" aria-controls="demo-menu" style="background: #007bff; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer;">Menu</button>
                        <div id="demo-menu" aria-hidden="true" style="display: none; background: white; border: 1px solid #ddd; padding: 10px; margin-top: 5px; border-radius: 4px;">
                            Menu content would appear here
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ ARIA attributes help screen readers understand interactive elements.</p>
            </div>

            <!-- Example 37: Form Accessibility -->
            <div class="demo-container">
                <h3>Example 37: Accessible Form Design</h3>
                <div class="code-block">
                    <pre><code>&lt;form&gt;
    &lt;fieldset&gt;
        &lt;legend&gt;Personal Information&lt;/legend&gt;

        &lt;label for="name"&gt;Full Name *&lt;/label&gt;
        &lt;input type="text" id="name" name="name" required
               aria-describedby="name-help"&gt;
        &lt;div id="name-help"&gt;Enter your first and last name&lt;/div&gt;

        &lt;label for="email"&gt;Email Address *&lt;/label&gt;
        &lt;input type="email" id="email" name="email" required
               aria-describedby="email-error"&gt;
        &lt;div id="email-error" role="alert" aria-live="polite"&gt;&lt;/div&gt;
    &lt;/fieldset&gt;
&lt;/form&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <form style="max-width: 400px;">
                        <fieldset style="border: 1px solid #ddd; padding: 20px; border-radius: 8px;">
                            <legend style="padding: 0 10px; font-weight: bold;">Personal Information</legend>

                            <div style="margin: 15px 0;">
                                <label for="demo-name-acc" style="display: block; margin-bottom: 5px; font-weight: 500;">Full Name *</label>
                                <input type="text" id="demo-name-acc" name="name" required aria-describedby="name-help" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                                <div id="name-help" style="font-size: 0.9em; color: #666; margin-top: 5px;">Enter your first and last name</div>
                            </div>

                            <div style="margin: 15px 0;">
                                <label for="demo-email-acc" style="display: block; margin-bottom: 5px; font-weight: 500;">Email Address *</label>
                                <input type="email" id="demo-email-acc" name="email" required aria-describedby="email-error" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                                <div id="email-error" role="alert" aria-live="polite" style="font-size: 0.9em; color: #dc3545; margin-top: 5px;"></div>
                            </div>
                        </fieldset>
                    </form>
                </div>
            </div>

            <!-- Example 38: Image Accessibility -->
            <div class="demo-container">
                <h3>Example 38: Proper Image Alt Text</h3>
                <div class="code-block">
                    <pre><code>&lt;!-- Informative image --&gt;
&lt;img src="sales-chart.png" alt="Sales increased 25% from Q1 to Q2 2024, reaching $2.5 million"&gt;

&lt;!-- Decorative image --&gt;
&lt;img src="decoration.png" alt="" role="presentation"&gt;

&lt;!-- Complex image with description --&gt;
&lt;figure&gt;
    &lt;img src="complex-chart.png" alt="Quarterly revenue breakdown by region"&gt;
    &lt;figcaption&gt;
        Detailed breakdown: North America $1.2M, Europe $800K, Asia $500K
    &lt;/figcaption&gt;
&lt;/figure&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; gap: 20px;">
                        <div>
                            <p><strong>Informative image:</strong></p>
                            <img src="https://via.placeholder.com/300x200/28a745/ffffff?text=Sales+Chart" alt="Sales increased 25% from Q1 to Q2 2024, reaching $2.5 million" style="border-radius: 8px;">
                        </div>

                        <div>
                            <p><strong>Complex image with description:</strong></p>
                            <figure style="margin: 0;">
                                <img src="https://via.placeholder.com/300x200/007bff/ffffff?text=Revenue+Chart" alt="Quarterly revenue breakdown by region" style="border-radius: 8px;">
                                <figcaption style="margin-top: 10px; font-style: italic; color: #666;">
                                    Detailed breakdown: North America $1.2M, Europe $800K, Asia $500K
                                </figcaption>
                            </figure>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ Alt text should describe the content and function of images, not just their appearance.</p>
            </div>

            <!-- Example 39: Skip Navigation -->
            <div class="demo-container">
                <h3>Example 39: Skip Navigation Link</h3>
                <div class="code-block">
                    <pre><code>&lt;a href="#main-content" class="skip-link"&gt;Skip to main content&lt;/a&gt;

&lt;style&gt;
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}
&lt;/style&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <div style="position: relative; background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <a href="#main-content" style="position: absolute; top: -40px; left: 6px; background: #000; color: #fff; padding: 8px; text-decoration: none; z-index: 1000; transition: top 0.3s;" onfocus="this.style.top='6px'" onblur="this.style.top='-40px'">Skip to main content</a>
                        <p><strong>Tab to see the skip link appear!</strong></p>
                        <p>This link helps keyboard users skip repetitive navigation.</p>
                    </div>
                </div>
            </div>

            <!-- Example 40: SEO Meta Tags -->
            <div class="demo-container">
                <h3>Example 40: SEO and Meta Tags</h3>
                <div class="code-block">
                    <pre><code>&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;

    &lt;!-- SEO Meta Tags --&gt;
    &lt;title&gt;HTML5 Tutorial - Learn Modern Web Development&lt;/title&gt;
    &lt;meta name="description" content="Complete HTML5 tutorial with 50+ examples. Learn semantic HTML, forms, accessibility, and modern web development best practices."&gt;
    &lt;meta name="keywords" content="HTML5, web development, tutorial, semantic HTML, accessibility"&gt;
    &lt;meta name="author" content="Web Dev Manual"&gt;

    &lt;!-- Open Graph for Social Media --&gt;
    &lt;meta property="og:title" content="HTML5 Tutorial - Learn Modern Web Development"&gt;
    &lt;meta property="og:description" content="Complete HTML5 tutorial with 50+ examples"&gt;
    &lt;meta property="og:image" content="https://example.com/html5-tutorial.jpg"&gt;
    &lt;meta property="og:url" content="https://example.com/html5-tutorial"&gt;
    &lt;meta property="og:type" content="article"&gt;

    &lt;!-- Twitter Card --&gt;
    &lt;meta name="twitter:card" content="summary_large_image"&gt;
    &lt;meta name="twitter:title" content="HTML5 Tutorial"&gt;
    &lt;meta name="twitter:description" content="Complete HTML5 tutorial with 50+ examples"&gt;
    &lt;meta name="twitter:image" content="https://example.com/html5-tutorial.jpg"&gt;

    &lt;!-- Favicon --&gt;
    &lt;link rel="icon" type="image/x-icon" href="/favicon.ico"&gt;
    &lt;link rel="apple-touch-icon" href="/apple-touch-icon.png"&gt;
&lt;/head&gt;</code></pre>
                </div>
                <p class="explanation">✅ Proper meta tags improve SEO and social media sharing.</p>
            </div>

            <!-- Example 41-50: Quick Reference Examples -->
            <div class="demo-container">
                <h3>Examples 41-50: Quick Reference & Advanced Techniques</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">

                    <!-- Example 41: Data Attributes -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;">
                        <h4>41. Data Attributes</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>&lt;div data-user-id="123" data-role="admin"&gt;
    User Profile
&lt;/div&gt;</code></pre>
                        </div>
                    </div>

                    <!-- Example 42: Microdata -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                        <h4>42. Microdata for SEO</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>&lt;div itemscope itemtype="http://schema.org/Person"&gt;
    &lt;span itemprop="name"&gt;John Doe&lt;/span&gt;
&lt;/div&gt;</code></pre>
                        </div>
                    </div>

                    <!-- Example 43: Progress Element -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                        <h4>43. Progress Bar</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>&lt;progress value="70" max="100"&gt;70%&lt;/progress&gt;</code></pre>
                        </div>
                        <progress value="70" max="100" style="width: 100%; margin-top: 10px;">70%</progress>
                    </div>

                    <!-- Example 44: Meter Element -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;">
                        <h4>44. Meter (Gauge)</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>&lt;meter value="6" min="0" max="10"&gt;6 out of 10&lt;/meter&gt;</code></pre>
                        </div>
                        <meter value="6" min="0" max="10" style="width: 100%; margin-top: 10px;">6 out of 10</meter>
                    </div>

                    <!-- Example 45: Details/Summary -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #6f42c1;">
                        <h4>45. Collapsible Content</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>&lt;details&gt;
    &lt;summary&gt;Click to expand&lt;/summary&gt;
    Hidden content here
&lt;/details&gt;</code></pre>
                        </div>
                        <details style="margin-top: 10px;">
                            <summary>Click to expand</summary>
                            <p style="margin: 10px 0;">Hidden content appears here when expanded!</p>
                        </details>
                    </div>

                    <!-- Example 46: Mark Element -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #fd7e14;">
                        <h4>46. Highlighted Text</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>&lt;p&gt;Search results for &lt;mark&gt;HTML5&lt;/mark&gt;&lt;/p&gt;</code></pre>
                        </div>
                        <p style="margin-top: 10px;">Search results for <mark>HTML5</mark></p>
                    </div>

                    <!-- Example 47: Time Element -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #20c997;">
                        <h4>47. Time Element</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>&lt;time datetime="2024-01-15T14:30"&gt;
    January 15, 2024 at 2:30 PM
&lt;/time&gt;</code></pre>
                        </div>
                        <time datetime="2024-01-15T14:30" style="display: block; margin-top: 10px; color: #666;">January 15, 2024 at 2:30 PM</time>
                    </div>

                    <!-- Example 48: Address Element -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #e83e8c;">
                        <h4>48. Contact Information</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>&lt;address&gt;
    &lt;a href="mailto:<EMAIL>"&gt;John Doe&lt;/a&gt;&lt;br&gt;
    123 Web Street&lt;br&gt;
    Internet City, IC 12345
&lt;/address&gt;</code></pre>
                        </div>
                        <address style="margin-top: 10px; font-style: italic; color: #666;">
                            <a href="mailto:<EMAIL>">John Doe</a><br>
                            123 Web Street<br>
                            Internet City, IC 12345
                        </address>
                    </div>

                    <!-- Example 49: Kbd Element -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #6c757d;">
                        <h4>49. Keyboard Input</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>&lt;p&gt;Press &lt;kbd&gt;Ctrl&lt;/kbd&gt; + &lt;kbd&gt;C&lt;/kbd&gt; to copy&lt;/p&gt;</code></pre>
                        </div>
                        <p style="margin-top: 10px;">Press <kbd style="background: #e9ecef; padding: 2px 4px; border-radius: 3px; font-family: monospace;">Ctrl</kbd> + <kbd style="background: #e9ecef; padding: 2px 4px; border-radius: 3px; font-family: monospace;">C</kbd> to copy</p>
                    </div>

                    <!-- Example 50: Code Element -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
                        <h4>50. Inline Code</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>&lt;p&gt;Use the &lt;code&gt;console.log()&lt;/code&gt; function&lt;/p&gt;</code></pre>
                        </div>
                        <p style="margin-top: 10px;">Use the <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px; font-family: monospace; color: #e83e8c;">console.log()</code> function</p>
                    </div>
                </div>
            </div>

            <!-- Summary -->
            <div class="demo-container" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; text-align: center;">
                <h3 style="color: white; margin-top: 0;">🎉 Congratulations! You've Mastered HTML5!</h3>
                <p style="font-size: 1.1em; margin: 20px 0;">You've learned 50+ examples covering:</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <div>✅ Basic HTML structure</div>
                    <div>✅ Text formatting</div>
                    <div>✅ Links and navigation</div>
                    <div>✅ Lists and tables</div>
                    <div>✅ Forms and inputs</div>
                    <div>✅ Semantic HTML5 elements</div>
                    <div>✅ Media elements</div>
                    <div>✅ Accessibility features</div>
                    <div>✅ SEO optimization</div>
                    <div>✅ Real-world examples</div>
                </div>
                <p style="font-size: 1.2em; margin-top: 30px;"><strong>You're now ready to build professional, accessible, and semantic websites!</strong></p>
            </div>
        </section>

        <section class="resources" id="resources">
            <h2>Additional Resources</h2>
            <div class="resource-grid">
                <div class="resource-card">
                    <h3>📖 Official Documentation</h3>
                    <ul>
                        <li><a href="https://developer.mozilla.org/en-US/docs/Web/HTML" target="_blank">MDN HTML Reference</a></li>
                        <li><a href="https://html.spec.whatwg.org/" target="_blank">HTML Living Standard</a></li>
                        <li><a href="https://www.w3.org/WAI/WCAG21/quickref/" target="_blank">WCAG 2.1 Guidelines</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>🛠️ Tools & Validators</h3>
                    <ul>
                        <li><a href="https://validator.w3.org/" target="_blank">W3C HTML Validator</a></li>
                        <li><a href="https://wave.webaim.org/" target="_blank">WAVE Accessibility Checker</a></li>
                        <li><a href="https://caniuse.com/" target="_blank">Can I Use</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>📚 Learning Resources</h3>
                    <ul>
                        <li><a href="https://web.dev/learn/html/" target="_blank">Web.dev HTML Course</a></li>
                        <li><a href="https://htmlreference.io/" target="_blank">HTML Reference</a></li>
                        <li><a href="https://a11yproject.com/" target="_blank">A11Y Project</a></li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <script src="assets/js/manual-scripts.js"></script>
    <script>
        // Canvas demo functionality
        function drawShapes() {
            const canvas = document.getElementById('demo-canvas');
            const ctx = canvas.getContext('2d');

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw rectangle
            ctx.fillStyle = '#3498db';
            ctx.fillRect(50, 50, 100, 80);

            // Draw circle
            ctx.beginPath();
            ctx.arc(250, 90, 40, 0, 2 * Math.PI);
            ctx.fillStyle = '#e74c3c';
            ctx.fill();

            // Draw text
            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.fillText('HTML5 Canvas!', 130, 160);
        }

        function clearCanvas() {
            const canvas = document.getElementById('demo-canvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // Example counter for demonstration
        let exampleCount = 0;
        function updateExampleCount() {
            exampleCount++;
            const counter = document.getElementById('example-counter');
            if (counter) {
                counter.textContent = `You've viewed ${exampleCount} examples!`;
            }
        }

        // Add click handlers to all demo containers
        document.addEventListener('DOMContentLoaded', function() {
            const demoContainers = document.querySelectorAll('.demo-container');
            demoContainers.forEach(container => {
                container.addEventListener('click', updateExampleCount);
            });

            // Initialize canvas
            drawShapes();

            // Add copy functionality to code blocks
            const copyButtons = document.querySelectorAll('.copy-btn');
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const codeBlock = this.previousElementSibling.querySelector('code');
                    if (codeBlock) {
                        navigator.clipboard.writeText(codeBlock.textContent).then(() => {
                            this.textContent = 'Copied!';
                            setTimeout(() => {
                                this.textContent = 'Copy Code';
                            }, 2000);
                        });
                    }
                });
            });

            // Add interactive features to forms
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('Form submitted successfully! (This is just a demo)');
                });
            });

            // Add hover effects to examples
            const examples = document.querySelectorAll('.live-demo');
            examples.forEach(example => {
                example.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'transform 0.3s ease';
                });

                example.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });

        // Progress tracking
        function trackProgress() {
            const sections = document.querySelectorAll('section');
            const totalSections = sections.length;
            let viewedSections = 0;

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('viewed');
                        viewedSections = document.querySelectorAll('section.viewed').length;
                        updateProgressBar(viewedSections, totalSections);
                    }
                });
            }, { threshold: 0.5 });

            sections.forEach(section => {
                observer.observe(section);
            });
        }

        function updateProgressBar(viewed, total) {
            const progress = (viewed / total) * 100;
            const progressBar = document.getElementById('progress-bar');
            if (progressBar) {
                progressBar.style.width = progress + '%';
            }
        }

        // Initialize progress tracking
        document.addEventListener('DOMContentLoaded', trackProgress);
    </script>

    <style>
        /* Additional styles for enhanced interactivity */
        .demo-container {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .demo-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .live-demo {
            transition: transform 0.3s ease;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
            transition: background-color 0.3s ease;
        }

        .copy-btn:hover {
            background: #218838;
        }

        .explanation {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 0 8px 8px 0;
            font-style: italic;
        }

        /* Progress bar styles */
        .progress-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .demo-container {
                margin: 1rem 0;
                padding: 1rem;
            }

            .code-block {
                font-size: 0.8rem;
                overflow-x: auto;
            }

            .live-demo {
                padding: 1rem;
            }
        }

        /* Accessibility improvements */
        .skip-link:focus {
            position: absolute;
            top: 6px;
            left: 6px;
            background: #000;
            color: #fff;
            padding: 8px;
            text-decoration: none;
            z-index: 1000;
            border-radius: 4px;
        }

        /* Print styles */
        @media print {
            .manual-nav,
            .copy-btn,
            .progress-container {
                display: none;
            }

            .demo-container {
                break-inside: avoid;
                margin: 1rem 0;
            }

            .code-block {
                background: #f8f9fa !important;
                color: #000 !important;
                border: 1px solid #ddd;
            }
        }
    </style>
</body>
</html>
