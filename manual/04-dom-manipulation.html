<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM Manipulation - Web Development Manual</title>
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .dom-playground {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            background: #f7fafc;
        }
        
        .element-demo {
            background: white;
            border: 1px solid #cbd5e0;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            min-height: 100px;
        }
        
        .control-panel {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin: 1rem 0;
        }
        
        .control-panel button {
            padding: 0.5rem 1rem;
            border: 1px solid #3182ce;
            background: #3182ce;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .control-panel button:hover {
            background: #2c5282;
        }
        
        .event-log {
            background: #1a202c;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Fira Code', monospace;
            max-height: 200px;
            overflow-y: auto;
            margin: 1rem 0;
        }
        
        .draggable-item {
            background: #4299e1;
            color: white;
            padding: 1rem;
            margin: 0.5rem;
            border-radius: 8px;
            cursor: move;
            user-select: none;
            transition: transform 0.2s;
        }
        
        .draggable-item:hover {
            transform: scale(1.05);
        }
        
        .drop-zone {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            color: #718096;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .drop-zone.drag-over {
            border-color: #4299e1;
            background: #ebf8ff;
        }
    </style>
</head>
<body>
    <nav class="manual-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>Web Dev Manual</h2>
            </div>
            <ul class="nav-links">
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#selecting">Selecting Elements</a></li>
                <li><a href="#manipulating">Manipulating Elements</a></li>
                <li><a href="#events">Event Handling</a></li>
                <li><a href="#advanced">Advanced Techniques</a></li>
                <li><a href="#resources">Resources</a></li>
            </ul>
            <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
        </div>
    </nav>

    <main class="manual-content">
        <header class="topic-header" id="introduction">
            <div class="header-content">
                <h1>DOM Manipulation</h1>
                <p class="header-description">
                    Master the Document Object Model (DOM) API to dynamically interact with HTML elements, handle events, 
                    and create interactive web experiences using JavaScript.
                </p>
                <div class="header-stats">
                    <span class="stat">🎯 Interactive Examples</span>
                    <span class="stat">⚡ Event Handling</span>
                    <span class="stat">🔧 Practical Techniques</span>
                </div>
            </div>
        </header>

        <section class="concepts" id="selecting">
            <h2>Selecting DOM Elements</h2>
            <p>Learn different methods to select and access HTML elements in the DOM.</p>
            
            <div class="demo-container">
                <h3>Selection Methods</h3>
                <div class="code-block">
                    <pre><code>// Modern selection methods (recommended)
const element = document.querySelector('#myId');
const elements = document.querySelectorAll('.myClass');
const firstParagraph = document.querySelector('p');
const allParagraphs = document.querySelectorAll('p');

// Complex selectors
const nestedElement = document.querySelector('.container > .item:first-child');
const attributeElement = document.querySelector('input[type="email"]');
const pseudoElement = document.querySelector('li:nth-child(odd)');

// Legacy methods (still useful)
const byId = document.getElementById('myId');
const byClass = document.getElementsByClassName('myClass');
const byTag = document.getElementsByTagName('p');
const byName = document.getElementsByName('username');

// Traversing the DOM
const parent = element.parentElement;
const children = element.children;
const firstChild = element.firstElementChild;
const lastChild = element.lastElementChild;
const nextSibling = element.nextElementSibling;
const previousSibling = element.previousElementSibling;

// Checking element relationships
const contains = parent.contains(element);
const matches = element.matches('.my-class');
const closest = element.closest('.container');</code></pre>
                </div>
                <button class="copy-btn" data-copy="selection">Copy Code</button>
            </div>

            <div class="demo-container">
                <h3>Interactive Selection Demo</h3>
                <div class="dom-playground">
                    <div class="element-demo" id="selection-demo">
                        <h4>Sample Content</h4>
                        <p class="demo-paragraph">This is the first paragraph.</p>
                        <p class="demo-paragraph highlight">This is the second paragraph with highlight class.</p>
                        <ul>
                            <li data-value="1">List item 1</li>
                            <li data-value="2">List item 2</li>
                            <li data-value="3">List item 3</li>
                        </ul>
                        <input type="text" placeholder="Sample input" class="demo-input">
                    </div>
                    
                    <div class="control-panel">
                        <button onclick="selectById()">Select by ID</button>
                        <button onclick="selectByClass()">Select by Class</button>
                        <button onclick="selectByTag()">Select by Tag</button>
                        <button onclick="selectByAttribute()">Select by Attribute</button>
                        <button onclick="selectComplex()">Complex Selector</button>
                        <button onclick="clearSelection()">Clear</button>
                    </div>
                    
                    <div class="event-log" id="selection-log">Selection results will appear here...</div>
                </div>
            </div>
        </section>

        <section class="examples" id="manipulating">
            <h2>Manipulating Elements</h2>
            <p>Learn how to modify element content, attributes, styles, and structure dynamically.</p>
            
            <div class="demo-container">
                <h3>Content and Attribute Manipulation</h3>
                <div class="code-block">
                    <pre><code>// Changing content
element.textContent = "New text content";
element.innerHTML = "&lt;strong&gt;New HTML content&lt;/strong&gt;";
element.innerText = "New visible text";

// Working with attributes
element.setAttribute('data-id', '123');
const value = element.getAttribute('data-id');
element.removeAttribute('class');
const hasAttribute = element.hasAttribute('data-id');

// Modern attribute properties
element.id = 'newId';
element.className = 'new-class another-class';
element.classList.add('active');
element.classList.remove('inactive');
element.classList.toggle('visible');
element.classList.contains('active');

// Style manipulation
element.style.color = 'red';
element.style.backgroundColor = 'yellow';
element.style.fontSize = '18px';
element.style.cssText = 'color: blue; font-size: 20px;';

// Getting computed styles
const styles = window.getComputedStyle(element);
const color = styles.getPropertyValue('color');</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Creating and Modifying Elements</h3>
                <div class="code-block">
                    <pre><code>// Creating new elements
const newDiv = document.createElement('div');
const newText = document.createTextNode('Hello World');
const newFragment = document.createDocumentFragment();

// Setting up the new element
newDiv.textContent = 'New element';
newDiv.className = 'dynamic-element';
newDiv.setAttribute('data-created', Date.now());

// Adding elements to the DOM
parent.appendChild(newDiv);
parent.insertBefore(newDiv, existingChild);
parent.insertAdjacentElement('beforebegin', newDiv);
parent.insertAdjacentHTML('afterend', '&lt;p&gt;New paragraph&lt;/p&gt;');

// Removing elements
element.remove();                    // Modern method
parent.removeChild(element);         // Legacy method

// Cloning elements
const clone = element.cloneNode(true);  // Deep clone
const shallowClone = element.cloneNode(false);</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Element Manipulation Playground</h3>
                <div class="dom-playground">
                    <div class="element-demo" id="manipulation-demo">
                        <div id="target-element" class="demo-box" style="background: #e2e8f0; padding: 1rem; border-radius: 8px;">
                            <h4>Target Element</h4>
                            <p>This element can be manipulated using the controls below.</p>
                        </div>
                    </div>
                    
                    <div class="control-panel">
                        <button onclick="changeText()">Change Text</button>
                        <button onclick="changeHTML()">Change HTML</button>
                        <button onclick="toggleClass()">Toggle Class</button>
                        <button onclick="changeStyle()">Change Style</button>
                        <button onclick="addElement()">Add Element</button>
                        <button onclick="removeElement()">Remove Element</button>
                    </div>
                    
                    <div>
                        <label>Custom Text: <input type="text" id="custom-text" placeholder="Enter custom text"></label>
                        <label>Background Color: <input type="color" id="bg-color" value="#e2e8f0"></label>
                    </div>
                </div>
            </div>
        </section>

        <section class="advanced" id="events">
            <h2>Event Handling</h2>
            <p>Master event handling to create interactive user experiences with proper event management.</p>
            
            <div class="demo-container">
                <h3>Event Listeners</h3>
                <div class="code-block">
                    <pre><code>// Adding event listeners
element.addEventListener('click', handleClick);
element.addEventListener('mouseover', handleMouseOver);
element.addEventListener('keydown', handleKeyDown);

// Event handler functions
function handleClick(event) {
    console.log('Element clicked!');
    console.log('Event type:', event.type);
    console.log('Target element:', event.target);
    console.log('Current target:', event.currentTarget);
}

// Arrow function event handlers
const handleMouseOver = (event) => {
    event.target.style.backgroundColor = 'lightblue';
};

// Event object properties
function handleKeyDown(event) {
    console.log('Key pressed:', event.key);
    console.log('Key code:', event.keyCode);
    console.log('Ctrl key:', event.ctrlKey);
    console.log('Shift key:', event.shiftKey);
    
    // Prevent default behavior
    if (event.key === 'Enter') {
        event.preventDefault();
    }
    
    // Stop event propagation
    event.stopPropagation();
}

// Removing event listeners
element.removeEventListener('click', handleClick);

// Event delegation
document.addEventListener('click', function(event) {
    if (event.target.matches('.button')) {
        console.log('Button clicked via delegation');
    }
});</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Interactive Event Demo</h3>
                <div class="dom-playground">
                    <div class="element-demo" id="event-demo">
                        <button id="click-btn" class="demo-button">Click Me</button>
                        <button id="double-click-btn" class="demo-button">Double Click Me</button>
                        <input type="text" id="key-input" placeholder="Type here to see key events">
                        <div id="mouse-area" style="background: #f0f0f0; padding: 2rem; margin: 1rem 0; border-radius: 8px;">
                            Mouse interaction area
                        </div>
                    </div>
                    
                    <div class="control-panel">
                        <button onclick="clearEventLog()">Clear Log</button>
                        <button onclick="toggleEventListeners()">Toggle Listeners</button>
                    </div>
                    
                    <div class="event-log" id="event-log">Event log will appear here...</div>
                </div>
            </div>
        </section>

        <section class="best-practices" id="advanced">
            <h2>Advanced DOM Techniques</h2>
            <p>Explore advanced DOM manipulation techniques including drag and drop, intersection observer, and performance optimization.</p>
            
            <div class="demo-container">
                <h3>Drag and Drop</h3>
                <div class="dom-playground">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                        <div>
                            <h4>Draggable Items</h4>
                            <div class="draggable-item" draggable="true" data-item="item1">Item 1</div>
                            <div class="draggable-item" draggable="true" data-item="item2">Item 2</div>
                            <div class="draggable-item" draggable="true" data-item="item3">Item 3</div>
                        </div>
                        <div>
                            <h4>Drop Zone</h4>
                            <div class="drop-zone" id="drop-zone">
                                Drop items here
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-container">
                <h3>Intersection Observer</h3>
                <div class="code-block">
                    <pre><code>// Intersection Observer for lazy loading and animations
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            // Element is visible
            entry.target.classList.add('animate-in');
            
            // Lazy load images
            if (entry.target.tagName === 'IMG') {
                entry.target.src = entry.target.dataset.src;
            }
            
            // Stop observing once loaded
            observer.unobserve(entry.target);
        }
    });
}, {
    threshold: 0.1,
    rootMargin: '50px'
});

// Observe elements
document.querySelectorAll('.lazy-load').forEach(el => {
    observer.observe(el);
});</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Performance Best Practices</h3>
                <div class="code-block">
                    <pre><code>// Batch DOM operations
const fragment = document.createDocumentFragment();
for (let i = 0; i < 1000; i++) {
    const item = document.createElement('li');
    item.textContent = `Item ${i}`;
    fragment.appendChild(item);
}
list.appendChild(fragment);

// Use requestAnimationFrame for animations
function animate() {
    // Animation code here
    element.style.transform = `translateX(${position}px)`;
    
    if (shouldContinue) {
        requestAnimationFrame(animate);
    }
}
requestAnimationFrame(animate);

// Debounce expensive operations
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

const debouncedSearch = debounce(performSearch, 300);
searchInput.addEventListener('input', debouncedSearch);</code></pre>
                </div>
            </div>
        </section>

        <section class="resources" id="resources">
            <h2>Additional Resources</h2>
            <div class="resource-grid">
                <div class="resource-card">
                    <h3>📖 Official Documentation</h3>
                    <ul>
                        <li><a href="https://developer.mozilla.org/en-US/docs/Web/API/Document_Object_Model" target="_blank">MDN DOM Reference</a></li>
                        <li><a href="https://dom.spec.whatwg.org/" target="_blank">DOM Living Standard</a></li>
                        <li><a href="https://developer.mozilla.org/en-US/docs/Web/Events" target="_blank">Event Reference</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>🛠️ Tools & Libraries</h3>
                    <ul>
                        <li><a href="https://jquery.com/" target="_blank">jQuery</a></li>
                        <li><a href="https://lodash.com/" target="_blank">Lodash</a></li>
                        <li><a href="https://github.com/davidjbradshaw/iframe-resizer" target="_blank">DOM Utilities</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>📚 Learning Resources</h3>
                    <ul>
                        <li><a href="https://javascript.info/document" target="_blank">JavaScript.info DOM</a></li>
                        <li><a href="https://web.dev/dom/" target="_blank">Web.dev DOM Guide</a></li>
                        <li><a href="https://eloquentjavascript.net/14_dom.html" target="_blank">Eloquent JavaScript DOM</a></li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <script src="assets/js/manual-scripts.js"></script>
    <script>
        // Selection demo functions
        function logToSelection(message) {
            const log = document.getElementById('selection-log');
            log.textContent += message + '\n';
            log.scrollTop = log.scrollHeight;
        }
        
        function selectById() {
            const element = document.getElementById('selection-demo');
            logToSelection(`Selected by ID: ${element.tagName} with id="${element.id}"`);
        }
        
        function selectByClass() {
            const elements = document.querySelectorAll('.demo-paragraph');
            logToSelection(`Selected by class: Found ${elements.length} elements with class "demo-paragraph"`);
        }
        
        function selectByTag() {
            const elements = document.querySelectorAll('li');
            logToSelection(`Selected by tag: Found ${elements.length} <li> elements`);
        }
        
        function selectByAttribute() {
            const elements = document.querySelectorAll('[data-value]');
            logToSelection(`Selected by attribute: Found ${elements.length} elements with data-value attribute`);
        }
        
        function selectComplex() {
            const element = document.querySelector('.demo-paragraph.highlight');
            logToSelection(`Complex selector: Found element with both classes: ${element ? 'Yes' : 'No'}`);
        }
        
        function clearSelection() {
            document.getElementById('selection-log').textContent = 'Selection results will appear here...';
        }
        
        // Manipulation demo functions
        function changeText() {
            const element = document.getElementById('target-element');
            const customText = document.getElementById('custom-text').value || 'Text changed!';
            element.querySelector('p').textContent = customText;
        }
        
        function changeHTML() {
            const element = document.getElementById('target-element');
            element.innerHTML = '<h4>HTML Changed!</h4><p><strong>Bold text</strong> and <em>italic text</em></p>';
        }
        
        function toggleClass() {
            const element = document.getElementById('target-element');
            element.classList.toggle('highlighted');
            if (!document.querySelector('style[data-demo]')) {
                const style = document.createElement('style');
                style.setAttribute('data-demo', 'true');
                style.textContent = '.highlighted { border: 3px solid #f56565; background: #fed7d7; }';
                document.head.appendChild(style);
            }
        }
        
        function changeStyle() {
            const element = document.getElementById('target-element');
            const bgColor = document.getElementById('bg-color').value;
            element.style.backgroundColor = bgColor;
            element.style.transform = 'scale(1.05)';
            element.style.transition = 'all 0.3s ease';
        }
        
        function addElement() {
            const container = document.getElementById('manipulation-demo');
            const newElement = document.createElement('div');
            newElement.textContent = `New element added at ${new Date().toLocaleTimeString()}`;
            newElement.style.cssText = 'background: #c6f6d5; padding: 0.5rem; margin: 0.5rem 0; border-radius: 4px;';
            container.appendChild(newElement);
        }
        
        function removeElement() {
            const container = document.getElementById('manipulation-demo');
            const lastChild = container.lastElementChild;
            if (lastChild && lastChild.id !== 'target-element') {
                lastChild.remove();
            }
        }
        
        // Event handling demo
        let eventListenersActive = true;
        
        function logEvent(message) {
            const log = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            log.textContent += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        function clearEventLog() {
            document.getElementById('event-log').textContent = 'Event log will appear here...';
        }
        
        function setupEventListeners() {
            const clickBtn = document.getElementById('click-btn');
            const doubleClickBtn = document.getElementById('double-click-btn');
            const keyInput = document.getElementById('key-input');
            const mouseArea = document.getElementById('mouse-area');
            
            clickBtn.addEventListener('click', () => logEvent('Button clicked!'));
            doubleClickBtn.addEventListener('dblclick', () => logEvent('Button double-clicked!'));
            keyInput.addEventListener('keydown', (e) => logEvent(`Key pressed: ${e.key}`));
            mouseArea.addEventListener('mouseenter', () => logEvent('Mouse entered area'));
            mouseArea.addEventListener('mouseleave', () => logEvent('Mouse left area'));
        }
        
        function toggleEventListeners() {
            eventListenersActive = !eventListenersActive;
            logEvent(`Event listeners ${eventListenersActive ? 'enabled' : 'disabled'}`);
        }
        
        // Drag and drop functionality
        function setupDragAndDrop() {
            const draggableItems = document.querySelectorAll('.draggable-item');
            const dropZone = document.getElementById('drop-zone');
            
            draggableItems.forEach(item => {
                item.addEventListener('dragstart', (e) => {
                    e.dataTransfer.setData('text/plain', e.target.dataset.item);
                    e.target.style.opacity = '0.5';
                });
                
                item.addEventListener('dragend', (e) => {
                    e.target.style.opacity = '1';
                });
            });
            
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('drag-over');
            });
            
            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('drag-over');
            });
            
            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
                const itemId = e.dataTransfer.getData('text/plain');
                dropZone.innerHTML = `<p>Dropped: ${itemId}</p><button onclick="resetDropZone()">Reset</button>`;
            });
        }
        
        function resetDropZone() {
            document.getElementById('drop-zone').innerHTML = 'Drop items here';
        }
        
        // Initialize all demos
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            setupDragAndDrop();
            clearSelection();
            clearEventLog();
        });
    </script>
</body>
</html>
