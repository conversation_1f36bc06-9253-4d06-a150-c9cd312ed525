<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS3 Fundamentals - Web Development Manual</title>
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Demo styles for this page */
        .css-demo-box {
            width: 200px;
            height: 100px;
            background: linear-gradient(45deg, #3498db, #9b59b6);
            margin: 1rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .css-demo-box:hover {
            transform: scale(1.05) rotate(2deg);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .flexbox-demo {
            display: flex;
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .flex-item {
            background: #007bff;
            color: white;
            padding: 1rem;
            border-radius: 4px;
            text-align: center;
        }
        
        .grid-demo {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .grid-item {
            background: #28a745;
            color: white;
            padding: 1rem;
            border-radius: 4px;
            text-align: center;
        }
        
        .animation-demo {
            width: 100px;
            height: 100px;
            background: #ff6b6b;
            border-radius: 50%;
            animation: bounce 2s infinite;
            margin: 2rem auto;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            60% { transform: translateY(-15px); }
        }
        
        .variable-demo {
            --primary-color: #3498db;
            --secondary-color: #e74c3c;
            --border-radius: 8px;
            
            background: var(--primary-color);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius);
            border: 3px solid var(--secondary-color);
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="manual-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>Web Dev Manual</h2>
            </div>
            <ul class="nav-links">
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#selectors">Selectors</a></li>
                <li><a href="#box-model">Box Model</a></li>
                <li><a href="#layouts">Layouts</a></li>
                <li><a href="#animations">Animations</a></li>
                <li><a href="#modern-features">Modern CSS</a></li>
                <li><a href="#resources">Resources</a></li>
            </ul>
            <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
        </div>
    </nav>

    <main class="manual-content">
        <header class="topic-header" id="introduction">
            <div class="header-content">
                <h1>CSS3 Fundamentals</h1>
                <p class="header-description">
                    Master modern CSS3 features including selectors, layouts, animations, and cutting-edge properties. 
                    Learn to create beautiful, responsive designs with the latest CSS techniques.
                </p>
                <div class="header-stats">
                    <span class="stat">🎨 Visual Design</span>
                    <span class="stat">📱 Responsive Layouts</span>
                    <span class="stat">✨ Modern Features</span>
                </div>
            </div>
        </header>

        <section class="concepts" id="css-basics">
            <h2>CSS Fundamentals: From Zero to Hero</h2>
            <p>Let's start from the absolute beginning and build up to complex CSS layouts with 50+ progressive examples.</p>

            <!-- Example 1: Your First CSS -->
            <div class="demo-container">
                <h3>Example 1: Your First CSS Style</h3>
                <div class="code-block">
                    <pre><code>&lt;!-- HTML --&gt;
&lt;p&gt;This is a paragraph.&lt;/p&gt;

&lt;!-- CSS --&gt;
&lt;style&gt;
p {
    color: blue;
}
&lt;/style&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <p style="color: blue;">This is a paragraph.</p>
                </div>
                <p class="explanation">✅ CSS changes how HTML elements look. Here we made the paragraph text blue.</p>
            </div>

            <!-- Example 2: Adding Background Color -->
            <div class="demo-container">
                <h3>Example 2: Adding Background Color</h3>
                <div class="code-block">
                    <pre><code>p {
    color: white;
    background-color: blue;
}</code></pre>
                </div>
                <div class="live-demo">
                    <p style="color: white; background-color: blue; padding: 10px;">This paragraph has white text on a blue background.</p>
                </div>
                <p class="explanation">✅ We can change both text color and background color.</p>
            </div>

            <!-- Example 3: Adding Padding -->
            <div class="demo-container">
                <h3>Example 3: Adding Space with Padding</h3>
                <div class="code-block">
                    <pre><code>p {
    color: white;
    background-color: blue;
    padding: 20px;
}</code></pre>
                </div>
                <div class="live-demo">
                    <p style="color: white; background-color: blue; padding: 20px;">This paragraph has padding (space inside the element).</p>
                </div>
                <p class="explanation">✅ Padding adds space inside an element, between the content and the border.</p>
            </div>

            <!-- Example 4: CSS Classes -->
            <div class="demo-container">
                <h3>Example 4: Using CSS Classes</h3>
                <div class="code-block">
                    <pre><code>&lt;!-- HTML --&gt;
&lt;p class="highlight"&gt;This paragraph has a class.&lt;/p&gt;
&lt;p&gt;This paragraph has no class.&lt;/p&gt;

&lt;!-- CSS --&gt;
.highlight {
    background-color: yellow;
    padding: 10px;
}</code></pre>
                </div>
                <div class="live-demo">
                    <p style="background-color: yellow; padding: 10px;">This paragraph has a class.</p>
                    <p>This paragraph has no class.</p>
                </div>
                <p class="explanation">✅ Classes let you style specific elements. Use a dot (.) before the class name in CSS.</p>
            </div>

            <!-- Example 5: Multiple Classes -->
            <div class="demo-container">
                <h3>Example 5: Multiple Classes on One Element</h3>
                <div class="code-block">
                    <pre><code>&lt;!-- HTML --&gt;
&lt;p class="highlight bold"&gt;This has two classes.&lt;/p&gt;

&lt;!-- CSS --&gt;
.highlight {
    background-color: yellow;
}

.bold {
    font-weight: bold;
    font-size: 18px;
}</code></pre>
                </div>
                <div class="live-demo">
                    <p style="background-color: yellow; font-weight: bold; font-size: 18px; padding: 10px;">This has two classes.</p>
                </div>
                <p class="explanation">✅ You can apply multiple classes to one element by separating them with spaces.</p>
            </div>

            <!-- Example 6: CSS IDs -->
            <div class="demo-container">
                <h3>Example 6: Using CSS IDs</h3>
                <div class="code-block">
                    <pre><code>&lt;!-- HTML --&gt;
&lt;h1 id="main-title"&gt;Main Title&lt;/h1&gt;
&lt;h1&gt;Regular Title&lt;/h1&gt;

&lt;!-- CSS --&gt;
#main-title {
    color: red;
    text-align: center;
}</code></pre>
                </div>
                <div class="live-demo">
                    <h1 style="color: red; text-align: center; margin: 10px 0;">Main Title</h1>
                    <h1 style="margin: 10px 0;">Regular Title</h1>
                </div>
                <p class="explanation">✅ IDs are unique identifiers. Use a hash (#) before the ID name in CSS. Each ID should only be used once per page.</p>
            </div>

            <!-- Example 7: Font Properties -->
            <div class="demo-container">
                <h3>Example 7: Changing Fonts</h3>
                <div class="code-block">
                    <pre><code>p {
    font-family: Arial, sans-serif;
    font-size: 16px;
    font-weight: bold;
    font-style: italic;
}</code></pre>
                </div>
                <div class="live-demo">
                    <p style="font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; font-style: italic;">This text has custom font properties.</p>
                </div>
                <p class="explanation">✅ Font properties control how text appears: family (typeface), size, weight (boldness), and style (italic).</p>
            </div>

            <!-- Example 8: Text Alignment -->
            <div class="demo-container">
                <h3>Example 8: Text Alignment</h3>
                <div class="code-block">
                    <pre><code>.left { text-align: left; }
.center { text-align: center; }
.right { text-align: right; }
.justify { text-align: justify; }</code></pre>
                </div>
                <div class="live-demo">
                    <p style="text-align: left; background: #f0f0f0; padding: 10px; margin: 5px 0;">Left aligned text</p>
                    <p style="text-align: center; background: #f0f0f0; padding: 10px; margin: 5px 0;">Center aligned text</p>
                    <p style="text-align: right; background: #f0f0f0; padding: 10px; margin: 5px 0;">Right aligned text</p>
                    <p style="text-align: justify; background: #f0f0f0; padding: 10px; margin: 5px 0;">Justified text spreads words evenly across the line, creating straight edges on both sides of the paragraph.</p>
                </div>
            </div>

            <!-- Example 9: Borders -->
            <div class="demo-container">
                <h3>Example 9: Adding Borders</h3>
                <div class="code-block">
                    <pre><code>.border-solid { border: 2px solid black; }
.border-dashed { border: 2px dashed red; }
.border-dotted { border: 2px dotted blue; }
.border-custom {
    border-top: 3px solid green;
    border-right: 1px dashed orange;
    border-bottom: 2px dotted purple;
    border-left: 4px solid pink;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="border: 2px solid black; padding: 10px; margin: 10px 0;">Solid border</div>
                    <div style="border: 2px dashed red; padding: 10px; margin: 10px 0;">Dashed border</div>
                    <div style="border: 2px dotted blue; padding: 10px; margin: 10px 0;">Dotted border</div>
                    <div style="border-top: 3px solid green; border-right: 1px dashed orange; border-bottom: 2px dotted purple; border-left: 4px solid pink; padding: 10px; margin: 10px 0;">Custom borders on each side</div>
                </div>
                <p class="explanation">✅ Borders can be solid, dashed, or dotted. You can style each side differently.</p>
            </div>

            <!-- Example 10: Margin vs Padding -->
            <div class="demo-container">
                <h3>Example 10: Understanding Margin vs Padding</h3>
                <div class="code-block">
                    <pre><code>.with-margin {
    background-color: lightblue;
    margin: 20px;
    padding: 10px;
}

.with-padding {
    background-color: lightgreen;
    padding: 20px;
    margin: 10px;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="background: #f0f0f0; padding: 20px;">
                        <div style="background-color: lightblue; margin: 20px; padding: 10px;">Margin: 20px, Padding: 10px</div>
                        <div style="background-color: lightgreen; padding: 20px; margin: 10px;">Padding: 20px, Margin: 10px</div>
                    </div>
                </div>
                <p class="explanation">✅ Margin is space OUTSIDE the element (between elements). Padding is space INSIDE the element (around content).</p>
            </div>

            <!-- Example 11: Width and Height -->
            <div class="demo-container">
                <h3>Example 11: Setting Width and Height</h3>
                <div class="code-block">
                    <pre><code>.box-small {
    width: 100px;
    height: 50px;
    background-color: red;
}

.box-large {
    width: 200px;
    height: 100px;
    background-color: blue;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="width: 100px; height: 50px; background-color: red; margin: 10px 0; color: white; display: flex; align-items: center; justify-content: center;">Small</div>
                    <div style="width: 200px; height: 100px; background-color: blue; margin: 10px 0; color: white; display: flex; align-items: center; justify-content: center;">Large</div>
                </div>
                <p class="explanation">✅ Width and height control the size of elements. You can use pixels (px), percentages (%), or other units.</p>
            </div>

            <!-- Example 12: Display Property -->
            <div class="demo-container">
                <h3>Example 12: Display Property</h3>
                <div class="code-block">
                    <pre><code>.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.none { display: none; }</code></pre>
                </div>
                <div class="live-demo">
                    <div style="background: #f0f0f0; padding: 10px;">
                        <span style="display: block; background: lightblue; padding: 5px; margin: 2px;">Block (takes full width)</span>
                        <span style="display: inline; background: lightgreen; padding: 5px; margin: 2px;">Inline</span>
                        <span style="display: inline; background: lightgreen; padding: 5px; margin: 2px;">Inline</span>
                        <span style="display: inline-block; background: lightyellow; padding: 5px; margin: 2px; width: 100px;">Inline-block</span>
                        <span style="display: inline-block; background: lightyellow; padding: 5px; margin: 2px; width: 100px;">Inline-block</span>
                    </div>
                </div>
                <p class="explanation">✅ Display controls how elements behave: block (full width), inline (flows with text), inline-block (flows but accepts width/height).</p>
            </div>

            <!-- Example 13: Colors (Different Ways) -->
            <div class="demo-container">
                <h3>Example 13: Different Ways to Specify Colors</h3>
                <div class="code-block">
                    <pre><code>.color-name { color: red; }
.color-hex { color: #3498db; }
.color-rgb { color: rgb(52, 152, 219); }
.color-rgba { color: rgba(52, 152, 219, 0.5); }
.color-hsl { color: hsl(204, 70%, 53%); }</code></pre>
                </div>
                <div class="live-demo">
                    <p style="color: red; margin: 5px 0;">Color name: red</p>
                    <p style="color: #3498db; margin: 5px 0;">Hex: #3498db</p>
                    <p style="color: rgb(52, 152, 219); margin: 5px 0;">RGB: rgb(52, 152, 219)</p>
                    <p style="color: rgba(52, 152, 219, 0.5); margin: 5px 0;">RGBA: rgba(52, 152, 219, 0.5) - 50% transparent</p>
                    <p style="color: hsl(204, 70%, 53%); margin: 5px 0;">HSL: hsl(204, 70%, 53%)</p>
                </div>
                <p class="explanation">✅ Colors can be specified as names, hex codes, RGB values, RGBA (with transparency), or HSL (hue, saturation, lightness).</p>
            </div>

            <!-- Example 14: Hover Effects -->
            <div class="demo-container">
                <h3>Example 14: Interactive Hover Effects</h3>
                <div class="code-block">
                    <pre><code>.hover-button {
    background-color: blue;
    color: white;
    padding: 10px 20px;
    border: none;
    cursor: pointer;
}

.hover-button:hover {
    background-color: red;
    transform: scale(1.1);
}</code></pre>
                </div>
                <div class="live-demo">
                    <button style="background-color: blue; color: white; padding: 10px 20px; border: none; cursor: pointer; transition: all 0.3s ease;"
                            onmouseover="this.style.backgroundColor='red'; this.style.transform='scale(1.1)'"
                            onmouseout="this.style.backgroundColor='blue'; this.style.transform='scale(1)'">
                        Hover over me!
                    </button>
                </div>
                <p class="explanation">✅ The :hover pseudo-class applies styles when users hover over elements. Great for interactive feedback!</p>
            </div>

            <!-- Example 15: CSS Transitions -->
            <div class="demo-container">
                <h3>Example 15: Smooth Transitions</h3>
                <div class="code-block">
                    <pre><code>.transition-box {
    width: 100px;
    height: 100px;
    background-color: blue;
    transition: all 0.3s ease;
}

.transition-box:hover {
    width: 150px;
    height: 150px;
    background-color: red;
    border-radius: 50%;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="width: 100px; height: 100px; background-color: blue; transition: all 0.3s ease; cursor: pointer;"
                         onmouseover="this.style.width='150px'; this.style.height='150px'; this.style.backgroundColor='red'; this.style.borderRadius='50%'"
                         onmouseout="this.style.width='100px'; this.style.height='100px'; this.style.backgroundColor='blue'; this.style.borderRadius='0'">
                    </div>
                    <p style="font-size: 0.9em; color: #666;">Hover over the box!</p>
                </div>
                <p class="explanation">✅ Transitions make changes smooth instead of instant. The 'all' keyword animates all changing properties.</p>
            </div>
        </section>

        <section class="examples" id="layout-basics">
            <h2>Building Layouts: From Simple to Complex</h2>
            <p>Now let's learn how to create layouts and position elements, building from simple concepts to complex designs.</p>

            <!-- Example 16: Float Layout (Old School) -->
            <div class="demo-container">
                <h3>Example 16: Float Layout (Historical Context)</h3>
                <div class="code-block">
                    <pre><code>.float-left {
    float: left;
    width: 30%;
    background-color: lightblue;
    padding: 10px;
    margin-right: 5%;
}

.float-right {
    float: right;
    width: 30%;
    background-color: lightgreen;
    padding: 10px;
    margin-left: 5%;
}

.clearfix::after {
    content: "";
    display: table;
    clear: both;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="overflow: hidden;">
                        <div style="float: left; width: 30%; background-color: lightblue; padding: 10px; margin-right: 5%; box-sizing: border-box;">Float Left</div>
                        <div style="float: right; width: 30%; background-color: lightgreen; padding: 10px; margin-left: 5%; box-sizing: border-box;">Float Right</div>
                        <div style="background-color: lightyellow; padding: 10px;">Center content flows around floated elements</div>
                    </div>
                </div>
                <p class="explanation">⚠️ Floats were used for layouts in the past, but Flexbox and Grid are much better modern solutions!</p>
            </div>

            <!-- Example 17: Position Property -->
            <div class="demo-container">
                <h3>Example 17: CSS Positioning</h3>
                <div class="code-block">
                    <pre><code>.relative {
    position: relative;
    top: 20px;
    left: 20px;
}

.absolute {
    position: absolute;
    top: 10px;
    right: 10px;
}

.fixed {
    position: fixed;
    bottom: 10px;
    right: 10px;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="position: relative; background: #f0f0f0; padding: 40px; height: 150px;">
                        <div style="background: lightblue; padding: 10px; display: inline-block;">Normal position</div>
                        <div style="position: relative; top: 20px; left: 20px; background: lightgreen; padding: 10px; display: inline-block;">Relative (moved from normal position)</div>
                        <div style="position: absolute; top: 10px; right: 10px; background: lightyellow; padding: 10px;">Absolute (positioned relative to parent)</div>
                    </div>
                </div>
                <p class="explanation">✅ Position controls how elements are placed: static (normal), relative (offset from normal), absolute (positioned relative to parent), fixed (positioned relative to viewport).</p>
            </div>

            <!-- Example 18: Flexbox Basics -->
            <div class="demo-container">
                <h3>Example 18: Introduction to Flexbox</h3>
                <div class="code-block">
                    <pre><code>.flex-container {
    display: flex;
    background-color: #f0f0f0;
    padding: 10px;
}

.flex-item {
    background-color: lightblue;
    padding: 20px;
    margin: 5px;
    flex: 1;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: flex; background-color: #f0f0f0; padding: 10px;">
                        <div style="background-color: lightblue; padding: 20px; margin: 5px; flex: 1; text-align: center;">Item 1</div>
                        <div style="background-color: lightgreen; padding: 20px; margin: 5px; flex: 1; text-align: center;">Item 2</div>
                        <div style="background-color: lightyellow; padding: 20px; margin: 5px; flex: 1; text-align: center;">Item 3</div>
                    </div>
                </div>
                <p class="explanation">✅ Flexbox makes it easy to create flexible layouts. Items automatically share space and align properly.</p>
            </div>

            <!-- Example 19: Flexbox Direction -->
            <div class="demo-container">
                <h3>Example 19: Flexbox Direction</h3>
                <div class="code-block">
                    <pre><code>.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-column-reverse { flex-direction: column-reverse; }</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h4>Row (default)</h4>
                            <div style="display: flex; flex-direction: row; background: #f0f0f0; padding: 10px;">
                                <div style="background: lightblue; padding: 10px; margin: 2px;">1</div>
                                <div style="background: lightgreen; padding: 10px; margin: 2px;">2</div>
                                <div style="background: lightyellow; padding: 10px; margin: 2px;">3</div>
                            </div>
                        </div>
                        <div>
                            <h4>Column</h4>
                            <div style="display: flex; flex-direction: column; background: #f0f0f0; padding: 10px; height: 120px;">
                                <div style="background: lightblue; padding: 10px; margin: 2px;">1</div>
                                <div style="background: lightgreen; padding: 10px; margin: 2px;">2</div>
                                <div style="background: lightyellow; padding: 10px; margin: 2px;">3</div>
                            </div>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ Flex-direction controls whether items flow horizontally (row) or vertically (column).</p>
            </div>

            <!-- Example 20: Flexbox Alignment -->
            <div class="demo-container">
                <h3>Example 20: Flexbox Alignment</h3>
                <div class="code-block">
                    <pre><code>.flex-center {
    display: flex;
    justify-content: center;    /* horizontal alignment */
    align-items: center;        /* vertical alignment */
    height: 200px;
}

.flex-space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="margin-bottom: 20px;">
                        <h4>Centered</h4>
                        <div style="display: flex; justify-content: center; align-items: center; height: 100px; background: #f0f0f0; border: 2px dashed #ccc;">
                            <div style="background: lightblue; padding: 15px;">Perfectly Centered!</div>
                        </div>
                    </div>
                    <div>
                        <h4>Space Between</h4>
                        <div style="display: flex; justify-content: space-between; align-items: center; background: #f0f0f0; padding: 10px;">
                            <div style="background: lightblue; padding: 10px;">Left</div>
                            <div style="background: lightgreen; padding: 10px;">Center</div>
                            <div style="background: lightyellow; padding: 10px;">Right</div>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ Justify-content aligns items horizontally, align-items aligns them vertically. This makes centering easy!</p>
            </div>

            <!-- Example 21: CSS Grid Basics -->
            <div class="demo-container">
                <h3>Example 21: Introduction to CSS Grid</h3>
                <div class="code-block">
                    <pre><code>.grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 10px;
    background-color: #f0f0f0;
    padding: 10px;
}

.grid-item {
    background-color: lightblue;
    padding: 20px;
    text-align: center;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; background-color: #f0f0f0; padding: 10px;">
                        <div style="background-color: lightblue; padding: 20px; text-align: center;">1</div>
                        <div style="background-color: lightgreen; padding: 20px; text-align: center;">2</div>
                        <div style="background-color: lightyellow; padding: 20px; text-align: center;">3</div>
                        <div style="background-color: lightcoral; padding: 20px; text-align: center;">4</div>
                        <div style="background-color: lightpink; padding: 20px; text-align: center;">5</div>
                        <div style="background-color: lightgray; padding: 20px; text-align: center;">6</div>
                    </div>
                </div>
                <p class="explanation">✅ CSS Grid creates a two-dimensional layout system. Perfect for complex layouts!</p>
            </div>

            <!-- Example 22: Grid Template Areas -->
            <div class="demo-container">
                <h3>Example 22: Grid Template Areas (Website Layout)</h3>
                <div class="code-block">
                    <pre><code>.website-grid {
    display: grid;
    grid-template-areas:
        "header header header"
        "sidebar main main"
        "footer footer footer";
    grid-template-rows: 60px 1fr 40px;
    height: 300px;
    gap: 10px;
}

.header { grid-area: header; }
.sidebar { grid-area: sidebar; }
.main { grid-area: main; }
.footer { grid-area: footer; }</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; grid-template-areas: 'header header header' 'sidebar main main' 'footer footer footer'; grid-template-rows: 60px 1fr 40px; height: 300px; gap: 10px;">
                        <div style="grid-area: header; background: lightblue; display: flex; align-items: center; justify-content: center; font-weight: bold;">Header</div>
                        <div style="grid-area: sidebar; background: lightgreen; display: flex; align-items: center; justify-content: center; font-weight: bold;">Sidebar</div>
                        <div style="grid-area: main; background: lightyellow; display: flex; align-items: center; justify-content: center; font-weight: bold;">Main Content</div>
                        <div style="grid-area: footer; background: lightcoral; display: flex; align-items: center; justify-content: center; font-weight: bold;">Footer</div>
                    </div>
                </div>
                <p class="explanation">✅ Grid template areas let you name sections and create layouts visually in your CSS!</p>
            </div>

            <!-- Example 23: Responsive Units -->
            <div class="demo-container">
                <h3>Example 23: Responsive Units (%, vw, vh, rem, em)</h3>
                <div class="code-block">
                    <pre><code>.percent { width: 50%; }           /* 50% of parent */
.viewport-width { width: 50vw; }   /* 50% of viewport width */
.viewport-height { height: 20vh; } /* 20% of viewport height */
.rem-unit { font-size: 2rem; }     /* 2x root font size */
.em-unit { font-size: 1.5em; }     /* 1.5x parent font size */</code></pre>
                </div>
                <div class="live-demo">
                    <div style="border: 2px solid #ccc; padding: 10px;">
                        <div style="width: 50%; background: lightblue; padding: 10px; margin: 5px 0;">50% width</div>
                        <div style="width: 50vw; background: lightgreen; padding: 10px; margin: 5px 0;">50vw width (50% of browser width)</div>
                        <div style="height: 20vh; background: lightyellow; padding: 10px; margin: 5px 0; display: flex; align-items: center;">20vh height (20% of browser height)</div>
                        <div style="font-size: 2rem; background: lightcoral; padding: 10px; margin: 5px 0;">2rem font size</div>
                        <div style="font-size: 1.5em; background: lightpink; padding: 10px; margin: 5px 0;">1.5em font size</div>
                    </div>
                </div>
                <p class="explanation">✅ Different units serve different purposes: % (relative to parent), vw/vh (viewport), rem (root font), em (parent font).</p>
            </div>

            <!-- Example 24: Media Queries (Responsive Design) -->
            <div class="demo-container">
                <h3>Example 24: Media Queries for Responsive Design</h3>
                <div class="code-block">
                    <pre><code>.responsive-box {
    background-color: lightblue;
    padding: 20px;
    text-align: center;
}

/* Mobile styles */
@media (max-width: 768px) {
    .responsive-box {
        background-color: lightgreen;
        font-size: 14px;
    }
}

/* Desktop styles */
@media (min-width: 769px) {
    .responsive-box {
        background-color: lightyellow;
        font-size: 18px;
    }
}</code></pre>
                </div>
                <div class="live-demo">
                    <div id="responsive-demo" style="background-color: lightyellow; padding: 20px; text-align: center; font-size: 18px; transition: all 0.3s ease;">
                        Resize your browser window to see me change!
                        <br><small>(Or click the buttons below to simulate)</small>
                        <br><br>
                        <button onclick="simulateMobile()" style="background: #28a745; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer;">Simulate Mobile</button>
                        <button onclick="simulateDesktop()" style="background: #007bff; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer;">Simulate Desktop</button>
                    </div>
                </div>
                <p class="explanation">✅ Media queries apply different styles based on screen size, making websites responsive!</p>
            </div>

            <!-- Example 25: Box Model Visualization -->
            <div class="demo-container">
                <h3>Example 25: The CSS Box Model</h3>
                <div class="code-block">
                    <pre><code>.box-model-demo {
    width: 200px;
    height: 100px;
    padding: 20px;
    border: 5px solid red;
    margin: 15px;
    background-color: lightblue;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="background: #f0f0f0; padding: 30px; text-align: center;">
                        <div style="display: inline-block; background: orange; padding: 15px;">
                            <span style="font-size: 12px; color: #333;">Margin</span>
                            <div style="background: red; padding: 5px;">
                                <span style="font-size: 12px; color: white;">Border</span>
                                <div style="background: green; padding: 20px;">
                                    <span style="font-size: 12px; color: white;">Padding</span>
                                    <div style="background: lightblue; padding: 20px; width: 200px; height: 100px; display: flex; align-items: center; justify-content: center;">
                                        <span style="font-size: 14px; color: #333; font-weight: bold;">Content<br>200px × 100px</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p style="text-align: center; margin-top: 15px; font-size: 14px;">
                        Total width = margin + border + padding + content + padding + border + margin
                    </p>
                </div>
                <p class="explanation">✅ Every element is a box with content, padding, border, and margin. Understanding this is crucial for layouts!</p>
            </div>
        </section>
            
            <div class="demo-container">
                <h3>Box Model Visualization</h3>
                <div class="live-demo">
                    <div class="box-model-demo">
                        <div class="box-model-container">
                            <div class="margin-area">
                                <span class="label">Margin</span>
                                <div class="border-area">
                                    <span class="label">Border</span>
                                    <div class="padding-area">
                                        <span class="label">Padding</span>
                                        <div class="content-area">
                                            <span class="label">Content</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="box-model-controls">
                            <label>Box Sizing: 
                                <select id="box-sizing-select">
                                    <option value="content-box">content-box</option>
                                    <option value="border-box">border-box</option>
                                </select>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-container">
                <h3>Box Model CSS</h3>
                <div class="code-block">
                    <pre><code>/* Traditional Box Model (content-box) */
.content-box {
    box-sizing: content-box; /* default */
    width: 200px;
    padding: 20px;
    border: 5px solid #333;
    margin: 10px;
    /* Total width = 200px + 40px + 10px + 20px = 270px */
}

/* Modern Box Model (border-box) */
.border-box {
    box-sizing: border-box;
    width: 200px;
    padding: 20px;
    border: 5px solid #333;
    margin: 10px;
    /* Total width = 200px (includes padding and border) */
}

/* Universal Border Box */
*, *::before, *::after {
    box-sizing: border-box;
}</code></pre>
                </div>
            </div>
        </section>

        <section class="advanced" id="advanced-css">
            <h2>Advanced CSS: Animations, Transforms & Real-World Examples</h2>
            <p>Now let's explore advanced CSS features and see how everything comes together in professional websites.</p>

            <!-- Example 26: CSS Transforms -->
            <div class="demo-container">
                <h3>Example 26: CSS Transforms</h3>
                <div class="code-block">
                    <pre><code>.transform-rotate { transform: rotate(45deg); }
.transform-scale { transform: scale(1.5); }
.transform-translate { transform: translate(50px, 30px); }
.transform-skew { transform: skew(20deg, 10deg); }
.transform-combined {
    transform: rotate(15deg) scale(1.2) translate(20px, 10px);
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 20px; padding: 20px; background: #f0f0f0;">
                        <div style="background: lightblue; padding: 15px; text-align: center;">Original</div>
                        <div style="background: lightgreen; padding: 15px; text-align: center; transform: rotate(45deg);">Rotated</div>
                        <div style="background: lightyellow; padding: 15px; text-align: center; transform: scale(1.5);">Scaled</div>
                        <div style="background: lightcoral; padding: 15px; text-align: center; transform: translate(20px, 10px);">Translated</div>
                        <div style="background: lightpink; padding: 15px; text-align: center; transform: skew(20deg, 10deg);">Skewed</div>
                        <div style="background: lightgray; padding: 15px; text-align: center; transform: rotate(15deg) scale(1.2) translate(10px, 5px);">Combined</div>
                    </div>
                </div>
                <p class="explanation">✅ Transforms change how elements appear without affecting document flow. Great for animations!</p>
            </div>

            <!-- Example 27: CSS Animations -->
            <div class="demo-container">
                <h3>Example 27: CSS Keyframe Animations</h3>
                <div class="code-block">
                    <pre><code>@keyframes bounce {
    0% { transform: translateY(0); }
    50% { transform: translateY(-30px); }
    100% { transform: translateY(0); }
}

@keyframes colorChange {
    0% { background-color: red; }
    25% { background-color: yellow; }
    50% { background-color: green; }
    75% { background-color: blue; }
    100% { background-color: red; }
}

.bounce { animation: bounce 1s infinite; }
.color-change { animation: colorChange 3s infinite; }</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: flex; gap: 30px; justify-content: center; align-items: center; padding: 40px; background: #f0f0f0;">
                        <div style="width: 60px; height: 60px; background: lightblue; border-radius: 50%; animation: bounce 1s infinite;">
                            <style>
                                @keyframes bounce {
                                    0% { transform: translateY(0); }
                                    50% { transform: translateY(-30px); }
                                    100% { transform: translateY(0); }
                                }
                            </style>
                        </div>
                        <div style="width: 60px; height: 60px; border-radius: 50%; animation: colorChange 3s infinite;">
                            <style>
                                @keyframes colorChange {
                                    0% { background-color: red; }
                                    25% { background-color: yellow; }
                                    50% { background-color: green; }
                                    75% { background-color: blue; }
                                    100% { background-color: red; }
                                }
                            </style>
                        </div>
                        <div style="width: 60px; height: 60px; background: lightgreen; animation: spin 2s linear infinite;">
                            <style>
                                @keyframes spin {
                                    from { transform: rotate(0deg); }
                                    to { transform: rotate(360deg); }
                                }
                            </style>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ Keyframe animations create complex motion and effects. Define keyframes at different percentages!</p>
            </div>

            <!-- Example 28: CSS Variables (Custom Properties) -->
            <div class="demo-container">
                <h3>Example 28: CSS Variables (Custom Properties)</h3>
                <div class="code-block">
                    <pre><code>:root {
    --primary-color: #3498db;
    --secondary-color: #e74c3c;
    --border-radius: 8px;
    --spacing: 1rem;
}

.card {
    background-color: var(--primary-color);
    border-radius: var(--border-radius);
    padding: var(--spacing);
    margin: var(--spacing);
    color: white;
}

.card.secondary {
    background-color: var(--secondary-color);
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="--primary-color: #3498db; --secondary-color: #e74c3c; --border-radius: 8px; --spacing: 1rem;">
                        <div style="background-color: var(--primary-color); border-radius: var(--border-radius); padding: var(--spacing); margin: var(--spacing); color: white;">
                            Primary Card using CSS Variables
                        </div>
                        <div style="background-color: var(--secondary-color); border-radius: var(--border-radius); padding: var(--spacing); margin: var(--spacing); color: white;">
                            Secondary Card using CSS Variables
                        </div>
                        <div style="margin-top: 20px;">
                            <button onclick="changeTheme()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Change Theme</button>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ CSS variables make it easy to maintain consistent designs and create themes!</p>
            </div>

            <!-- Example 29: Gradients -->
            <div class="demo-container">
                <h3>Example 29: CSS Gradients</h3>
                <div class="code-block">
                    <pre><code>.linear-gradient {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
}

.radial-gradient {
    background: radial-gradient(circle, #ff6b6b, #4ecdc4);
}

.conic-gradient {
    background: conic-gradient(#ff6b6b, #4ecdc4, #45b7d1, #ff6b6b);
}

.complex-gradient {
    background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 50%,
        #f093fb 100%);
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4); padding: 30px; color: white; text-align: center; border-radius: 8px;">Linear Gradient</div>
                        <div style="background: radial-gradient(circle, #ff6b6b, #4ecdc4); padding: 30px; color: white; text-align: center; border-radius: 8px;">Radial Gradient</div>
                        <div style="background: conic-gradient(#ff6b6b, #4ecdc4, #45b7d1, #ff6b6b); padding: 30px; color: white; text-align: center; border-radius: 8px;">Conic Gradient</div>
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); padding: 30px; color: white; text-align: center; border-radius: 8px;">Complex Gradient</div>
                    </div>
                </div>
                <p class="explanation">✅ Gradients create smooth color transitions. Linear goes in a direction, radial from center, conic rotates around.</p>
            </div>

            <!-- Example 30: Shadows and Effects -->
            <div class="demo-container">
                <h3>Example 30: Shadows and Visual Effects</h3>
                <div class="code-block">
                    <pre><code>.box-shadow {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.multiple-shadows {
    box-shadow:
        0 1px 3px rgba(0,0,0,0.12),
        0 1px 2px rgba(0,0,0,0.24);
}

.inset-shadow {
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; padding: 20px;">
                        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                            <h4 style="margin-top: 0;">Box Shadow</h4>
                            <p>Subtle shadow effect</p>
                        </div>
                        <div style="background: #3498db; color: white; padding: 20px; border-radius: 8px;">
                            <h4 style="margin-top: 0; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">Text Shadow</h4>
                            <p style="text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">Shadow on text</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);">
                            <h4 style="margin-top: 0;">Multiple Shadows</h4>
                            <p>Layered shadow effect</p>
                        </div>
                        <div style="background: #f0f0f0; padding: 20px; border-radius: 8px; box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);">
                            <h4 style="margin-top: 0;">Inset Shadow</h4>
                            <p>Shadow goes inward</p>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ Shadows add depth and dimension. Box-shadow for elements, text-shadow for text, inset for inner shadows.</p>
            </div>

            <!-- Example 31: Pseudo-elements -->
            <div class="demo-container">
                <h3>Example 31: Pseudo-elements (::before and ::after)</h3>
                <div class="code-block">
                    <pre><code>.quote::before {
    content: """;
    font-size: 2em;
    color: #3498db;
}

.quote::after {
    content: """;
    font-size: 2em;
    color: #3498db;
}

.badge::after {
    content: "NEW";
    background: red;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7em;
    margin-left: 10px;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="padding: 20px; background: #f9f9f9; border-radius: 8px;">
                        <p style="font-style: italic; font-size: 1.2em; margin: 20px 0;">
                            <span style="font-size: 2em; color: #3498db; line-height: 0; vertical-align: -0.2em;">"</span>
                            This is a quote with decorative quotation marks
                            <span style="font-size: 2em; color: #3498db; line-height: 0; vertical-align: -0.2em;">"</span>
                        </p>
                        <div style="margin-top: 30px;">
                            <span style="background: #e74c3c; color: white; padding: 10px 15px; border-radius: 4px;">
                                Product Name
                                <span style="background: red; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.7em; margin-left: 10px;">NEW</span>
                            </span>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ Pseudo-elements add content to your page without extra HTML. Great for decorative elements!</p>
            </div>

            <!-- Example 32: CSS Grid Advanced -->
            <div class="demo-container">
                <h3>Example 32: Advanced CSS Grid Layout</h3>
                <div class="code-block">
                    <pre><code>.advanced-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    grid-auto-rows: minmax(100px, auto);
    gap: 20px;
}

.grid-item-large {
    grid-column: span 2;
    grid-row: span 2;
}

.grid-item-wide {
    grid-column: span 2;
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); grid-auto-rows: minmax(80px, auto); gap: 15px; padding: 20px; background: #f0f0f0;">
                        <div style="background: lightblue; padding: 15px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">Item 1</div>
                        <div style="background: lightgreen; padding: 15px; display: flex; align-items: center; justify-content: center; border-radius: 8px; grid-column: span 2;">Wide Item</div>
                        <div style="background: lightyellow; padding: 15px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">Item 3</div>
                        <div style="background: lightcoral; padding: 15px; display: flex; align-items: center; justify-content: center; border-radius: 8px; grid-column: span 2; grid-row: span 2;">Large Item</div>
                        <div style="background: lightpink; padding: 15px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">Item 5</div>
                        <div style="background: lightgray; padding: 15px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">Item 6</div>
                    </div>
                </div>
                <p class="explanation">✅ Advanced Grid features like auto-fit, minmax, and span create flexible, responsive layouts!</p>
            </div>

            <!-- Example 33: Flexbox Advanced -->
            <div class="demo-container">
                <h3>Example 33: Advanced Flexbox Techniques</h3>
                <div class="code-block">
                    <pre><code>.flex-advanced {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: stretch;
}

.flex-item-grow {
    flex: 1;        /* grow to fill space */
}

.flex-item-fixed {
    flex: 0 0 200px; /* don't grow/shrink, fixed 200px */
}

.flex-item-shrink {
    flex: 0 1 auto;  /* can shrink but not grow */
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: flex; flex-wrap: wrap; gap: 10px; padding: 20px; background: #f0f0f0; border-radius: 8px;">
                        <div style="flex: 1; background: lightblue; padding: 15px; border-radius: 4px; min-width: 120px;">Grows (flex: 1)</div>
                        <div style="flex: 0 0 150px; background: lightgreen; padding: 15px; border-radius: 4px;">Fixed 150px</div>
                        <div style="flex: 2; background: lightyellow; padding: 15px; border-radius: 4px; min-width: 120px;">Grows 2x (flex: 2)</div>
                        <div style="flex: 0 1 auto; background: lightcoral; padding: 15px; border-radius: 4px;">Auto width</div>
                    </div>
                </div>
                <p class="explanation">✅ Flex properties control how items grow, shrink, and take up space in flexible layouts.</p>
            </div>

            <!-- Example 34: CSS Filters -->
            <div class="demo-container">
                <h3>Example 34: CSS Filters and Effects</h3>
                <div class="code-block">
                    <pre><code>.filter-blur { filter: blur(2px); }
.filter-brightness { filter: brightness(1.5); }
.filter-contrast { filter: contrast(1.5); }
.filter-grayscale { filter: grayscale(100%); }
.filter-sepia { filter: sepia(100%); }
.filter-hue-rotate { filter: hue-rotate(90deg); }
.filter-combined {
    filter: brightness(1.2) contrast(1.1) saturate(1.3);
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; padding: 20px;">
                        <div style="text-align: center;">
                            <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); border-radius: 50%; margin: 0 auto 10px;"></div>
                            <small>Original</small>
                        </div>
                        <div style="text-align: center;">
                            <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); border-radius: 50%; margin: 0 auto 10px; filter: blur(2px);"></div>
                            <small>Blur</small>
                        </div>
                        <div style="text-align: center;">
                            <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); border-radius: 50%; margin: 0 auto 10px; filter: brightness(1.5);"></div>
                            <small>Brightness</small>
                        </div>
                        <div style="text-align: center;">
                            <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); border-radius: 50%; margin: 0 auto 10px; filter: grayscale(100%);"></div>
                            <small>Grayscale</small>
                        </div>
                        <div style="text-align: center;">
                            <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); border-radius: 50%; margin: 0 auto 10px; filter: sepia(100%);"></div>
                            <small>Sepia</small>
                        </div>
                        <div style="text-align: center;">
                            <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); border-radius: 50%; margin: 0 auto 10px; filter: hue-rotate(90deg);"></div>
                            <small>Hue Rotate</small>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ CSS filters apply visual effects like blur, brightness, and color adjustments to elements.</p>
            </div>

            <!-- Example 35: Complete Website Layout -->
            <div class="demo-container">
                <h3>Example 35: Complete Modern Website (Everything Together!)</h3>
                <div class="code-block">
                    <pre><code>/* Modern website using all CSS concepts */
.modern-website {
    display: grid;
    grid-template-areas:
        "header header"
        "nav main"
        "footer footer";
    grid-template-columns: 250px 1fr;
    grid-template-rows: auto 1fr auto;
    min-height: 100vh;
    font-family: 'Arial', sans-serif;
}

.header {
    grid-area: header;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav {
    grid-area: nav;
    background: #f8f9fa;
    padding: 1rem;
}

.main {
    grid-area: main;
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

@media (max-width: 768px) {
    .modern-website {
        grid-template-areas:
            "header"
            "nav"
            "main"
            "footer";
        grid-template-columns: 1fr;
    }
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; grid-template-areas: 'header header' 'nav main' 'footer footer'; grid-template-columns: 200px 1fr; grid-template-rows: auto 1fr auto; height: 500px; font-family: Arial, sans-serif; border: 1px solid #ddd; border-radius: 12px; overflow: hidden;">
                        <!-- Header -->
                        <header style="grid-area: header; background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 1.5rem; display: flex; justify-content: space-between; align-items: center;">
                            <h1 style="margin: 0; font-size: 1.5rem;">Modern Site</h1>
                            <nav style="display: flex; gap: 1rem;">
                                <a href="#" style="color: white; text-decoration: none;">Home</a>
                                <a href="#" style="color: white; text-decoration: none;">About</a>
                                <a href="#" style="color: white; text-decoration: none;">Contact</a>
                            </nav>
                        </header>

                        <!-- Sidebar Navigation -->
                        <nav style="grid-area: nav; background: #f8f9fa; padding: 1rem;">
                            <h3 style="margin-top: 0; color: #333; font-size: 1rem;">Categories</h3>
                            <ul style="list-style: none; padding: 0; margin: 0;">
                                <li style="margin: 0.5rem 0;"><a href="#" style="color: #007bff; text-decoration: none; display: block; padding: 0.5rem; border-radius: 4px; transition: background 0.3s;" onmouseover="this.style.background='#e9ecef'" onmouseout="this.style.background='transparent'">Web Design</a></li>
                                <li style="margin: 0.5rem 0;"><a href="#" style="color: #007bff; text-decoration: none; display: block; padding: 0.5rem; border-radius: 4px; transition: background 0.3s;" onmouseover="this.style.background='#e9ecef'" onmouseout="this.style.background='transparent'">Development</a></li>
                                <li style="margin: 0.5rem 0;"><a href="#" style="color: #007bff; text-decoration: none; display: block; padding: 0.5rem; border-radius: 4px; transition: background 0.3s;" onmouseover="this.style.background='#e9ecef'" onmouseout="this.style.background='transparent'">Tutorials</a></li>
                            </ul>
                        </nav>

                        <!-- Main Content -->
                        <main style="grid-area: main; padding: 1.5rem; display: grid; grid-template-columns: 1fr; gap: 1rem; overflow-y: auto;">
                            <div style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 1rem; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'">
                                <h3 style="margin-top: 0; color: #333;">CSS Grid Layout</h3>
                                <p style="color: #666; margin-bottom: 0;">Learn how to create complex layouts with CSS Grid.</p>
                            </div>

                            <div style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 1rem; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'">
                                <h3 style="margin-top: 0; color: #333;">Flexbox Mastery</h3>
                                <p style="color: #666; margin-bottom: 0;">Master flexible layouts with Flexbox.</p>
                            </div>

                            <div style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 1rem; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'">
                                <h3 style="margin-top: 0; color: #333;">CSS Animations</h3>
                                <p style="color: #666; margin-bottom: 0;">Create engaging animations with CSS.</p>
                            </div>
                        </main>

                        <!-- Footer -->
                        <footer style="grid-area: footer; background: #343a40; color: white; padding: 1rem; text-align: center;">
                            <p style="margin: 0; font-size: 0.9rem;">&copy; 2024 Modern Website. Built with CSS Grid & Flexbox.</p>
                        </footer>
                    </div>
                </div>
                <p class="explanation">🎉 <strong>Congratulations!</strong> This combines Grid, Flexbox, gradients, shadows, transitions, and responsive design!</p>
            </div>
        </section>
            
            <div class="demo-container">
                <h3>Flexbox Layout</h3>
                <div class="live-demo">
                    <div class="flexbox-demo" id="flex-container">
                        <div class="flex-item">Item 1</div>
                        <div class="flex-item">Item 2</div>
                        <div class="flex-item">Item 3</div>
                    </div>
                    <div class="layout-controls">
                        <label>Justify Content: 
                            <select id="justify-content">
                                <option value="flex-start">flex-start</option>
                                <option value="center">center</option>
                                <option value="flex-end">flex-end</option>
                                <option value="space-between">space-between</option>
                                <option value="space-around">space-around</option>
                                <option value="space-evenly">space-evenly</option>
                            </select>
                        </label>
                        <label>Align Items: 
                            <select id="align-items">
                                <option value="stretch">stretch</option>
                                <option value="flex-start">flex-start</option>
                                <option value="center">center</option>
                                <option value="flex-end">flex-end</option>
                            </select>
                        </label>
                        <label>Direction: 
                            <select id="flex-direction">
                                <option value="row">row</option>
                                <option value="column">column</option>
                                <option value="row-reverse">row-reverse</option>
                                <option value="column-reverse">column-reverse</option>
                            </select>
                        </label>
                    </div>
                </div>
            </div>

            <div class="demo-container">
                <h3>CSS Grid Layout</h3>
                <div class="live-demo">
                    <div class="grid-demo" id="grid-container">
                        <div class="grid-item">1</div>
                        <div class="grid-item">2</div>
                        <div class="grid-item">3</div>
                        <div class="grid-item">4</div>
                        <div class="grid-item">5</div>
                        <div class="grid-item">6</div>
                    </div>
                    <div class="layout-controls">
                        <label>Columns: 
                            <select id="grid-columns">
                                <option value="repeat(3, 1fr)">3 equal columns</option>
                                <option value="1fr 2fr 1fr">1-2-1 ratio</option>
                                <option value="repeat(auto-fit, minmax(100px, 1fr))">Auto-fit</option>
                                <option value="200px 1fr 100px">Fixed-Flexible-Fixed</option>
                            </select>
                        </label>
                        <label>Gap: 
                            <input type="range" id="grid-gap" min="0" max="30" value="16">
                            <span id="gap-value">16px</span>
                        </label>
                    </div>
                </div>
            </div>
        </section>

        <section class="best-practices" id="animations">
            <h2>CSS Animations and Transitions</h2>
            <p>Create smooth, performant animations using CSS transitions and keyframe animations.</p>
            
            <div class="demo-container">
                <h3>CSS Transitions</h3>
                <div class="code-block">
                    <pre><code>/* Basic Transition */
.button {
    background-color: #3498db;
    transition: background-color 0.3s ease;
}

.button:hover {
    background-color: #2980b9;
}

/* Multiple Properties */
.card {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>CSS Keyframe Animations</h3>
                <div class="live-demo">
                    <div class="animation-demo"></div>
                    <div class="animation-controls">
                        <button onclick="toggleAnimation()">Toggle Animation</button>
                        <button onclick="changeAnimation('spin')">Spin</button>
                        <button onclick="changeAnimation('pulse')">Pulse</button>
                        <button onclick="changeAnimation('bounce')">Bounce</button>
                    </div>
                </div>
                
                <div class="code-block">
                    <pre><code>@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-30px); }
    60% { transform: translateY(-15px); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.animated {
    animation: bounce 2s infinite;
}</code></pre>
                </div>
            </div>
        </section>

        <section class="modern-features" id="best-practices">
            <h2>CSS Best Practices & Quick Reference (Examples 36-50+)</h2>
            <p>Let's complete our CSS journey with best practices, modern features, and a comprehensive quick reference.</p>

            <!-- Examples 36-50: Quick Reference Grid -->
            <div class="demo-container">
                <h3>Examples 36-50: CSS Quick Reference & Advanced Techniques</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px;">

                    <!-- Example 36: CSS Specificity -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;">
                        <h4>36. CSS Specificity</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>/* Specificity: 0,0,0,1 */
p { color: blue; }

/* Specificity: 0,0,1,0 */
.text { color: red; }

/* Specificity: 0,1,0,0 */
#title { color: green; }

/* Specificity: 1,0,0,0 */
style="color: purple"</code></pre>
                        </div>
                        <div style="margin-top: 10px;">
                            <p style="color: blue;">Element selector (weakest)</p>
                            <p style="color: red;" class="text">Class selector</p>
                            <p style="color: green;" id="demo-title">ID selector</p>
                            <p style="color: purple;">Inline style (strongest)</p>
                        </div>
                    </div>

                    <!-- Example 37: CSS Reset/Normalize -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                        <h4>37. CSS Reset</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>/* Basic CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
}</code></pre>
                        </div>
                        <p style="font-size: 0.9em; color: #666; margin-top: 10px;">Removes browser default styles for consistent starting point.</p>
                    </div>

                    <!-- Example 38: CSS Naming Conventions -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                        <h4>38. BEM Naming Convention</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>/* Block */
.card { }

/* Element */
.card__title { }
.card__content { }

/* Modifier */
.card--featured { }
.card__title--large { }</code></pre>
                        </div>
                        <p style="font-size: 0.9em; color: #666; margin-top: 10px;">BEM: Block__Element--Modifier for maintainable CSS.</p>
                    </div>

                    <!-- Example 39: CSS Container Queries -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;">
                        <h4>39. Container Queries (Modern)</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>.container {
    container-type: inline-size;
}

@container (min-width: 400px) {
    .card {
        display: flex;
        gap: 1rem;
    }
}</code></pre>
                        </div>
                        <p style="font-size: 0.9em; color: #666; margin-top: 10px;">Responsive design based on container size, not viewport.</p>
                    </div>

                    <!-- Example 40: CSS Logical Properties -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #6f42c1;">
                        <h4>40. Logical Properties</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>/* Instead of margin-left */
margin-inline-start: 1rem;

/* Instead of margin-top */
margin-block-start: 1rem;

/* Instead of width */
inline-size: 100%;

/* Instead of height */
block-size: 200px;</code></pre>
                        </div>
                        <p style="font-size: 0.9em; color: #666; margin-top: 10px;">Works with different writing modes and languages.</p>
                    </div>

                    <!-- Example 41: CSS Clamp Function -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #fd7e14;">
                        <h4>41. Clamp Function</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>/* Responsive font size */
font-size: clamp(1rem, 4vw, 3rem);

/* Responsive width */
width: clamp(300px, 50%, 800px);

/* min, preferred, max */</code></pre>
                        </div>
                        <div style="margin-top: 10px;">
                            <p style="font-size: clamp(0.8rem, 2vw, 1.2rem);">This text scales with viewport!</p>
                        </div>
                    </div>

                    <!-- Example 42: CSS Aspect Ratio -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #20c997;">
                        <h4>42. Aspect Ratio</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>.video-container {
    aspect-ratio: 16 / 9;
    background: #000;
}

.square {
    aspect-ratio: 1;
}</code></pre>
                        </div>
                        <div style="margin-top: 10px;">
                            <div style="aspect-ratio: 16/9; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9em;">16:9 Aspect Ratio</div>
                        </div>
                    </div>

                    <!-- Example 43: CSS Gap Property -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #e83e8c;">
                        <h4>43. Gap Property</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>/* Works with Grid and Flexbox */
.grid {
    display: grid;
    gap: 1rem;
}

.flex {
    display: flex;
    gap: 0.5rem;
}</code></pre>
                        </div>
                        <div style="margin-top: 10px; display: flex; gap: 0.5rem;">
                            <div style="background: lightblue; padding: 0.5rem; border-radius: 4px; flex: 1; text-align: center; font-size: 0.8em;">Item</div>
                            <div style="background: lightgreen; padding: 0.5rem; border-radius: 4px; flex: 1; text-align: center; font-size: 0.8em;">Item</div>
                            <div style="background: lightyellow; padding: 0.5rem; border-radius: 4px; flex: 1; text-align: center; font-size: 0.8em;">Item</div>
                        </div>
                    </div>

                    <!-- Example 44: CSS Scroll Behavior -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #6c757d;">
                        <h4>44. Smooth Scrolling</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>html {
    scroll-behavior: smooth;
}

.scroll-container {
    scroll-snap-type: y mandatory;
}

.scroll-item {
    scroll-snap-align: start;
}</code></pre>
                        </div>
                        <p style="font-size: 0.9em; color: #666; margin-top: 10px;">Creates smooth scrolling animations for anchor links.</p>
                    </div>

                    <!-- Example 45: CSS Object Fit -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
                        <h4>45. Object Fit</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    object-position: center;
}</code></pre>
                        </div>
                        <div style="margin-top: 10px;">
                            <div style="width: 100%; height: 60px; background: url('https://via.placeholder.com/400x200/3498db/ffffff?text=Image') center/cover; border-radius: 4px;"></div>
                            <p style="font-size: 0.8em; color: #666; margin-top: 5px;">Image maintains aspect ratio and fills container</p>
                        </div>
                    </div>

                    <!-- Example 46: CSS Backdrop Filter -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;">
                        <h4>46. Backdrop Filter</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>.glass {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}</code></pre>
                        </div>
                        <div style="margin-top: 10px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); padding: 20px; border-radius: 8px; position: relative;">
                            <div style="background: rgba(255,255,255,0.2); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.3); padding: 15px; border-radius: 8px; color: white; text-align: center; font-size: 0.9em;">Glass Effect</div>
                        </div>
                    </div>

                    <!-- Example 47: CSS Scroll Margin -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                        <h4>47. Scroll Margin</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>/* Offset for fixed headers */
section {
    scroll-margin-top: 80px;
}

/* When linking to sections */
#section1:target {
    scroll-margin-top: 100px;
}</code></pre>
                        </div>
                        <p style="font-size: 0.9em; color: #666; margin-top: 10px;">Prevents content from hiding behind fixed headers.</p>
                    </div>

                    <!-- Example 48: CSS Overscroll Behavior -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                        <h4>48. Overscroll Behavior</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>.modal {
    overscroll-behavior: contain;
}

body {
    overscroll-behavior-y: none;
}</code></pre>
                        </div>
                        <p style="font-size: 0.9em; color: #666; margin-top: 10px;">Controls scroll chaining and bounce effects.</p>
                    </div>

                    <!-- Example 49: CSS Accent Color -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;">
                        <h4>49. Accent Color</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>:root {
    accent-color: #3498db;
}

/* Affects checkboxes, radio buttons,
   range sliders, progress bars */</code></pre>
                        </div>
                        <div style="margin-top: 10px;">
                            <input type="checkbox" style="accent-color: #3498db; margin-right: 8px;" checked>
                            <input type="radio" style="accent-color: #3498db; margin-right: 8px;" checked>
                            <input type="range" style="accent-color: #3498db;" value="70">
                        </div>
                    </div>

                    <!-- Example 50: CSS Performance Tips -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #6f42c1;">
                        <h4>50. Performance Tips</h4>
                        <div class="code-block" style="font-size: 0.8em;">
                            <pre><code>/* Use transform for animations */
.animate {
    transform: translateX(100px);
    transition: transform 0.3s ease;
}

/* Avoid animating layout properties */
/* Bad: width, height, margin, padding */
/* Good: transform, opacity */

/* Use will-change sparingly */
.will-animate {
    will-change: transform;
}</code></pre>
                        </div>
                        <p style="font-size: 0.9em; color: #666; margin-top: 10px;">Transform and opacity are GPU-accelerated!</p>
                    </div>
                </div>
            </div>

            <!-- CSS Learning Path Summary -->
            <div class="demo-container" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; text-align: center;">
                <h3 style="color: white; margin-top: 0;">🎉 Your CSS Journey: From Zero to Hero!</h3>
                <p style="font-size: 1.1em; margin: 20px 0;">You've mastered 50+ CSS concepts in logical progression:</p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; text-align: left;">
                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #fff; margin-top: 0;">🔰 Foundation (1-15)</h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li>Basic styling & colors</li>
                            <li>Classes & IDs</li>
                            <li>Fonts & text</li>
                            <li>Borders & spacing</li>
                            <li>Display & positioning</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #fff; margin-top: 0;">🏗️ Layouts (16-25)</h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li>Flexbox mastery</li>
                            <li>CSS Grid layouts</li>
                            <li>Responsive units</li>
                            <li>Media queries</li>
                            <li>Box model</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #fff; margin-top: 0;">✨ Advanced (26-35)</h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li>Transforms & animations</li>
                            <li>CSS variables</li>
                            <li>Gradients & filters</li>
                            <li>Pseudo-elements</li>
                            <li>Complete websites</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #fff; margin-top: 0;">🚀 Modern (36-50)</h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li>Best practices</li>
                            <li>Modern CSS features</li>
                            <li>Performance tips</li>
                            <li>Professional techniques</li>
                            <li>Future-ready skills</li>
                        </ul>
                    </div>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin-top: 30px;">
                    <h4 style="color: #fff; margin-top: 0;">🎯 What You Can Build Now:</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px;">
                        <div>✅ Responsive websites</div>
                        <div>✅ Modern layouts</div>
                        <div>✅ Interactive animations</div>
                        <div>✅ Professional designs</div>
                        <div>✅ Mobile-first sites</div>
                        <div>✅ Accessible interfaces</div>
                    </div>
                </div>

                <p style="font-size: 1.2em; margin-top: 30px; font-weight: bold;">You're now ready to create beautiful, modern, responsive websites with CSS!</p>
            </div>
        </section>
            
            <div class="demo-container">
                <h3>CSS Custom Properties (Variables)</h3>
                <div class="live-demo">
                    <div class="variable-demo" id="variable-demo">
                        CSS Custom Properties Demo
                    </div>
                    <div class="variable-controls">
                        <label>Primary Color: 
                            <input type="color" id="primary-color" value="#3498db">
                        </label>
                        <label>Secondary Color: 
                            <input type="color" id="secondary-color" value="#e74c3c">
                        </label>
                        <label>Border Radius: 
                            <input type="range" id="border-radius" min="0" max="50" value="8">
                            <span id="radius-value">8px</span>
                        </label>
                    </div>
                </div>
                
                <div class="code-block">
                    <pre><code>:root {
    --primary-color: #3498db;
    --secondary-color: #e74c3c;
    --border-radius: 8px;
    --spacing-unit: 1rem;
}

.component {
    background: var(--primary-color);
    border: 2px solid var(--secondary-color);
    border-radius: var(--border-radius);
    padding: calc(var(--spacing-unit) * 2);
}

/* Dynamic theming */
[data-theme="dark"] {
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
}</code></pre>
                </div>
            </div>
        </section>

        <section class="resources" id="resources">
            <h2>Additional Resources</h2>
            <div class="resource-grid">
                <div class="resource-card">
                    <h3>📖 Official Documentation</h3>
                    <ul>
                        <li><a href="https://developer.mozilla.org/en-US/docs/Web/CSS" target="_blank">MDN CSS Reference</a></li>
                        <li><a href="https://www.w3.org/Style/CSS/" target="_blank">W3C CSS Specifications</a></li>
                        <li><a href="https://css-tricks.com/" target="_blank">CSS-Tricks</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>🛠️ Tools & Generators</h3>
                    <ul>
                        <li><a href="https://flexboxfroggy.com/" target="_blank">Flexbox Froggy</a></li>
                        <li><a href="https://cssgrid.io/" target="_blank">CSS Grid Course</a></li>
                        <li><a href="https://animista.net/" target="_blank">Animista</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>📚 Learning Resources</h3>
                    <ul>
                        <li><a href="https://web.dev/learn/css/" target="_blank">Web.dev CSS Course</a></li>
                        <li><a href="https://cssreference.io/" target="_blank">CSS Reference</a></li>
                        <li><a href="https://every-layout.dev/" target="_blank">Every Layout</a></li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <script src="assets/js/manual-scripts.js"></script>
    <script>
        // Mobile/Desktop simulation for responsive demo
        function simulateMobile() {
            const demo = document.getElementById('responsive-demo');
            demo.style.backgroundColor = 'lightgreen';
            demo.style.fontSize = '14px';
            demo.innerHTML = 'Mobile View Simulated!<br><small>Background: lightgreen, Font: 14px</small><br><br><button onclick="simulateMobile()" style="background: #28a745; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer;">Simulate Mobile</button><button onclick="simulateDesktop()" style="background: #007bff; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer;">Simulate Desktop</button>';
        }

        function simulateDesktop() {
            const demo = document.getElementById('responsive-demo');
            demo.style.backgroundColor = 'lightyellow';
            demo.style.fontSize = '18px';
            demo.innerHTML = 'Desktop View Simulated!<br><small>Background: lightyellow, Font: 18px</small><br><br><button onclick="simulateMobile()" style="background: #28a745; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer;">Simulate Mobile</button><button onclick="simulateDesktop()" style="background: #007bff; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer;">Simulate Desktop</button>';
        }

        // Theme changer for CSS variables demo
        function changeTheme() {
            const demo = document.querySelector('[style*="--primary-color"]');
            const currentPrimary = demo.style.getPropertyValue('--primary-color');

            if (currentPrimary === '#3498db' || !currentPrimary) {
                demo.style.setProperty('--primary-color', '#e74c3c');
                demo.style.setProperty('--secondary-color', '#3498db');
                demo.style.setProperty('--border-radius', '20px');
            } else {
                demo.style.setProperty('--primary-color', '#3498db');
                demo.style.setProperty('--secondary-color', '#e74c3c');
                demo.style.setProperty('--border-radius', '8px');
            }
        }

        // Interactive example counter
        let exampleCount = 0;
        function updateExampleCount() {
            exampleCount++;
            console.log(`You've interacted with ${exampleCount} examples!`);
        }

        // Add interactivity to all demo containers
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers to demo containers
            const demoContainers = document.querySelectorAll('.demo-container');
            demoContainers.forEach((container, index) => {
                container.addEventListener('click', function() {
                    updateExampleCount();
                    // Add a subtle animation
                    this.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 200);
                });
            });

            // Add copy functionality to code blocks
            const copyButtons = document.querySelectorAll('.copy-btn');
            copyButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const codeBlock = this.previousElementSibling.querySelector('code');
                    if (codeBlock) {
                        navigator.clipboard.writeText(codeBlock.textContent).then(() => {
                            this.textContent = 'Copied!';
                            this.style.background = '#28a745';
                            setTimeout(() => {
                                this.textContent = 'Copy Code';
                                this.style.background = '#007bff';
                            }, 2000);
                        });
                    }
                });
            });

            // Add smooth scrolling for navigation
            const navLinks = document.querySelectorAll('.nav-links a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Progress tracking
            const sections = document.querySelectorAll('section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('viewed');
                        updateProgressBar();
                    }
                });
            }, { threshold: 0.3 });

            sections.forEach(section => {
                observer.observe(section);
            });

            function updateProgressBar() {
                const totalSections = sections.length;
                const viewedSections = document.querySelectorAll('section.viewed').length;
                const progress = (viewedSections / totalSections) * 100;

                // Create progress bar if it doesn't exist
                let progressBar = document.getElementById('css-progress-bar');
                if (!progressBar) {
                    const progressContainer = document.createElement('div');
                    progressContainer.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 4px;
                        background: rgba(0,0,0,0.1);
                        z-index: 1000;
                    `;

                    progressBar = document.createElement('div');
                    progressBar.id = 'css-progress-bar';
                    progressBar.style.cssText = `
                        height: 100%;
                        background: linear-gradient(90deg, #667eea, #764ba2);
                        width: 0%;
                        transition: width 0.3s ease;
                    `;

                    progressContainer.appendChild(progressBar);
                    document.body.appendChild(progressContainer);
                }

                progressBar.style.width = progress + '%';
            }

            // Add hover effects to live demos
            const liveDemos = document.querySelectorAll('.live-demo');
            liveDemos.forEach(demo => {
                demo.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'transform 0.3s ease';
                });

                demo.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Initialize responsive demo
            simulateDesktop();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Press 'C' to copy the currently focused code block
            if (e.key === 'c' || e.key === 'C') {
                const focusedElement = document.activeElement;
                const codeBlock = focusedElement.closest('.demo-container')?.querySelector('code');
                if (codeBlock) {
                    navigator.clipboard.writeText(codeBlock.textContent);
                    console.log('Code copied to clipboard!');
                }
            }

            // Press 'N' to go to next section
            if (e.key === 'n' || e.key === 'N') {
                const currentSection = document.querySelector('section.viewed:last-child');
                const nextSection = currentSection?.nextElementSibling;
                if (nextSection && nextSection.tagName === 'SECTION') {
                    nextSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });

        // Add CSS for enhanced interactivity
        const style = document.createElement('style');
        style.textContent = `
            .demo-container {
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .demo-container:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }

            .live-demo {
                transition: transform 0.3s ease;
            }

            .copy-btn {
                background: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 10px;
                transition: all 0.3s ease;
                font-size: 0.9em;
            }

            .copy-btn:hover {
                background: #0056b3;
                transform: translateY(-1px);
            }

            .explanation {
                background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
                border-left: 4px solid #28a745;
                padding: 12px 16px;
                margin: 15px 0;
                border-radius: 0 8px 8px 0;
                font-style: italic;
                position: relative;
            }

            .explanation::before {
                content: "💡";
                position: absolute;
                left: -2px;
                top: 50%;
                transform: translateY(-50%);
                background: #28a745;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
            }

            section.viewed {
                opacity: 1;
                transform: translateY(0);
            }

            section {
                opacity: 0.8;
                transform: translateY(10px);
                transition: all 0.6s ease;
            }

            @media (max-width: 768px) {
                .demo-container {
                    margin: 1rem 0;
                    padding: 1rem;
                }

                .code-block {
                    font-size: 0.8rem;
                    overflow-x: auto;
                }

                .live-demo {
                    padding: 1rem;
                }

                .copy-btn {
                    font-size: 0.8em;
                    padding: 6px 12px;
                }
            }

            @media print {
                .manual-nav,
                .copy-btn,
                #css-progress-bar {
                    display: none;
                }

                .demo-container {
                    break-inside: avoid;
                    margin: 1rem 0;
                }

                .code-block {
                    background: #f8f9fa !important;
                    color: #000 !important;
                    border: 1px solid #ddd;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
        
        // Flexbox controls
        document.getElementById('justify-content').addEventListener('change', function() {
            document.getElementById('flex-container').style.justifyContent = this.value;
        });
        
        document.getElementById('align-items').addEventListener('change', function() {
            document.getElementById('flex-container').style.alignItems = this.value;
        });
        
        document.getElementById('flex-direction').addEventListener('change', function() {
            document.getElementById('flex-container').style.flexDirection = this.value;
        });
        
        // Grid controls
        document.getElementById('grid-columns').addEventListener('change', function() {
            document.getElementById('grid-container').style.gridTemplateColumns = this.value;
        });
        
        document.getElementById('grid-gap').addEventListener('input', function() {
            document.getElementById('grid-container').style.gap = this.value + 'px';
            document.getElementById('gap-value').textContent = this.value + 'px';
        });
        
        // Animation controls
        let isAnimating = true;
        function toggleAnimation() {
            const demo = document.querySelector('.animation-demo');
            isAnimating = !isAnimating;
            demo.style.animationPlayState = isAnimating ? 'running' : 'paused';
        }
        
        function changeAnimation(type) {
            const demo = document.querySelector('.animation-demo');
            demo.style.animation = `${type} 2s infinite`;
        }
        
        // CSS Variables controls
        document.getElementById('primary-color').addEventListener('input', function() {
            document.getElementById('variable-demo').style.setProperty('--primary-color', this.value);
        });
        
        document.getElementById('secondary-color').addEventListener('input', function() {
            document.getElementById('variable-demo').style.setProperty('--secondary-color', this.value);
        });
        
        document.getElementById('border-radius').addEventListener('input', function() {
            document.getElementById('variable-demo').style.setProperty('--border-radius', this.value + 'px');
            document.getElementById('radius-value').textContent = this.value + 'px';
        });
    </script>
    
    <style>
        /* Additional demo styles */
        .box-model-container {
            max-width: 400px;
            margin: 2rem auto;
        }
        
        .margin-area {
            background: #ffeb3b;
            padding: 20px;
            position: relative;
        }
        
        .border-area {
            background: #ff9800;
            padding: 15px;
            position: relative;
        }
        
        .padding-area {
            background: #4caf50;
            padding: 20px;
            position: relative;
        }
        
        .content-area {
            background: #2196f3;
            padding: 30px;
            color: white;
            text-align: center;
            position: relative;
        }
        
        .label {
            position: absolute;
            top: 5px;
            left: 5px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .layout-controls, .animation-controls, .variable-controls {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .layout-controls label, .animation-controls label, .variable-controls label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
    </style>
</body>
</html>
