<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Fundamentals - Web Development Manual</title>
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Fira Code', monospace;
            margin: 1rem 0;
            min-height: 100px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .js-playground {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .code-editor {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Fira Code', monospace;
            border: none;
            resize: vertical;
            min-height: 200px;
        }
        
        .variable-demo {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .function-demo {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .object-demo {
            background: #f0fff4;
            border: 1px solid #c6f6d5;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="manual-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>Web Dev Manual</h2>
            </div>
            <ul class="nav-links">
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#variables">Variables</a></li>
                <li><a href="#functions">Functions</a></li>
                <li><a href="#objects">Objects & Arrays</a></li>
                <li><a href="#control-flow">Control Flow</a></li>
                <li><a href="#modern-js">Modern JS</a></li>
                <li><a href="#resources">Resources</a></li>
            </ul>
            <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
        </div>
    </nav>

    <main class="manual-content">
        <header class="topic-header" id="introduction">
            <div class="header-content">
                <h1>JavaScript Fundamentals</h1>
                <p class="header-description">
                    Master the core concepts of JavaScript including variables, functions, objects, and modern ES2024 features. 
                    Build a solid foundation for web development with interactive examples and practical exercises.
                </p>
                <div class="header-stats">
                    <span class="stat">⚡ Interactive Playground</span>
                    <span class="stat">🔧 Practical Examples</span>
                    <span class="stat">🚀 Modern Features</span>
                </div>
            </div>
        </header>

        <section class="concepts" id="js-basics">
            <h2>JavaScript Fundamentals: From Zero to Hero</h2>
            <p>Let's start from the absolute beginning and build up to complex JavaScript applications with 50+ progressive examples.</p>

            <!-- Example 1: Your First JavaScript -->
            <div class="demo-container">
                <h3>Example 1: Your First JavaScript Code</h3>
                <div class="code-block">
                    <pre><code>&lt;script&gt;
alert("Hello, World!");
&lt;/script&gt;</code></pre>
                </div>
                <div class="live-demo">
                    <button onclick="alert('Hello, World!')" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Click to see your first JavaScript!</button>
                </div>
                <p class="explanation">✅ JavaScript runs in the browser and can interact with users through alerts, page changes, and more!</p>
            </div>

            <!-- Example 2: Console.log -->
            <div class="demo-container">
                <h3>Example 2: Using console.log() to Debug</h3>
                <div class="code-block">
                    <pre><code>console.log("Hello, World!");
console.log("This appears in the browser console");
console.log("Press F12 to open developer tools");</code></pre>
                </div>
                <div class="live-demo">
                    <button onclick="console.log('Hello from the console!'); alert('Check the browser console (F12)!')" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Run console.log()</button>
                    <div style="background: #1e1e1e; color: #00ff00; padding: 10px; margin-top: 10px; border-radius: 4px; font-family: monospace;">
                        > Hello, World!<br>
                        > This appears in the browser console<br>
                        > Press F12 to open developer tools
                    </div>
                </div>
                <p class="explanation">✅ console.log() is essential for debugging. It shows output in the browser's developer console.</p>
            </div>

            <!-- Example 3: Variables with let -->
            <div class="demo-container">
                <h3>Example 3: Creating Variables with let</h3>
                <div class="code-block">
                    <pre><code>let message = "Hello, JavaScript!";
console.log(message);

let number = 42;
console.log(number);

// Variables can be changed
message = "Hello, World!";
console.log(message);</code></pre>
                </div>
                <div class="live-demo">
                    <div id="variable-demo-1" style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace;">
                        <div>let message = "Hello, JavaScript!"</div>
                        <div style="color: #28a745;">→ Hello, JavaScript!</div>
                        <div>let number = 42</div>
                        <div style="color: #28a745;">→ 42</div>
                        <div>message = "Hello, World!"</div>
                        <div style="color: #28a745;">→ Hello, World!</div>
                    </div>
                    <button onclick="runVariableExample()" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px;">Run Example</button>
                </div>
                <p class="explanation">✅ Variables store data. Use 'let' to create variables that can be changed later.</p>
            </div>

            <!-- Example 4: Constants with const -->
            <div class="demo-container">
                <h3>Example 4: Constants with const</h3>
                <div class="code-block">
                    <pre><code>const name = "Alice";
console.log(name);

const age = 25;
console.log(age);

// This would cause an error:
// name = "Bob"; // TypeError: Assignment to constant variable</code></pre>
                </div>
                <div class="live-demo">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace;">
                        <div>const name = "Alice"</div>
                        <div style="color: #28a745;">→ Alice</div>
                        <div>const age = 25</div>
                        <div style="color: #28a745;">→ 25</div>
                        <div style="color: #dc3545;">name = "Bob" // ❌ Error!</div>
                    </div>
                </div>
                <p class="explanation">✅ Use 'const' for values that won't change. This prevents accidental modifications.</p>
            </div>

            <!-- Example 5: Data Types -->
            <div class="demo-container">
                <h3>Example 5: JavaScript Data Types</h3>
                <div class="code-block">
                    <pre><code>// String (text)
let text = "Hello World";

// Number
let count = 42;
let price = 19.99;

// Boolean (true/false)
let isActive = true;
let isComplete = false;

// Check types
console.log(typeof text);     // "string"
console.log(typeof count);    // "number"
console.log(typeof isActive); // "boolean"</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                            <strong>String</strong><br>
                            <code>"Hello World"</code><br>
                            <small>Text data</small>
                        </div>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                            <strong>Number</strong><br>
                            <code>42, 19.99</code><br>
                            <small>Numeric data</small>
                        </div>
                        <div style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                            <strong>Boolean</strong><br>
                            <code>true, false</code><br>
                            <small>True/false values</small>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ JavaScript has different data types for different kinds of information.</p>
            </div>

            <!-- Example 6: String Operations -->
            <div class="demo-container">
                <h3>Example 6: Working with Strings</h3>
                <div class="code-block">
                    <pre><code>let firstName = "John";
let lastName = "Doe";

// Concatenation (joining strings)
let fullName = firstName + " " + lastName;
console.log(fullName); // "John Doe"

// Template literals (modern way)
let greeting = `Hello, ${firstName}!`;
console.log(greeting); // "Hello, John!"

// String methods
console.log(fullName.length);        // 8
console.log(fullName.toUpperCase()); // "JOHN DOE"
console.log(fullName.toLowerCase()); // "john doe"</code></pre>
                </div>
                <div class="live-demo">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <div style="margin: 10px 0;">
                            <strong>String Concatenation:</strong><br>
                            <code>"John" + " " + "Doe" = "John Doe"</code>
                        </div>
                        <div style="margin: 10px 0;">
                            <strong>Template Literal:</strong><br>
                            <code>`Hello, ${firstName}!` = "Hello, John!"</code>
                        </div>
                        <div style="margin: 10px 0;">
                            <strong>String Methods:</strong><br>
                            <code>length: 8, UPPERCASE, lowercase</code>
                        </div>
                        <button onclick="runStringExample()" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Try String Operations</button>
                    </div>
                </div>
                <p class="explanation">✅ Strings can be joined, modified, and have useful methods for text manipulation.</p>
            </div>

            <!-- Example 7: Numbers and Math -->
            <div class="demo-container">
                <h3>Example 7: Numbers and Math Operations</h3>
                <div class="code-block">
                    <pre><code>let a = 10;
let b = 3;

console.log(a + b);  // 13 (addition)
console.log(a - b);  // 7  (subtraction)
console.log(a * b);  // 30 (multiplication)
console.log(a / b);  // 3.333... (division)
console.log(a % b);  // 1  (remainder/modulo)

// Math object
console.log(Math.round(3.7));  // 4
console.log(Math.floor(3.7));  // 3
console.log(Math.ceil(3.2));   // 4
console.log(Math.random());    // Random number 0-1</code></pre>
                </div>
                <div class="live-demo">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h4 style="margin-top: 0;">Basic Math</h4>
                            <div style="font-family: monospace;">
                                10 + 3 = <span style="color: #28a745;">13</span><br>
                                10 - 3 = <span style="color: #28a745;">7</span><br>
                                10 * 3 = <span style="color: #28a745;">30</span><br>
                                10 / 3 = <span style="color: #28a745;">3.33</span><br>
                                10 % 3 = <span style="color: #28a745;">1</span>
                            </div>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h4 style="margin-top: 0;">Math Object</h4>
                            <div style="font-family: monospace;">
                                Math.round(3.7) = <span style="color: #28a745;">4</span><br>
                                Math.floor(3.7) = <span style="color: #28a745;">3</span><br>
                                Math.ceil(3.2) = <span style="color: #28a745;">4</span><br>
                                Math.random() = <span style="color: #28a745;" id="random-number">0.123</span>
                            </div>
                            <button onclick="updateRandomNumber()" style="background: #28a745; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-top: 10px; font-size: 0.9em;">New Random</button>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ JavaScript can perform all basic math operations plus advanced functions through the Math object.</p>
            </div>

            <!-- Example 8: Booleans and Comparisons -->
            <div class="demo-container">
                <h3>Example 8: Booleans and Comparisons</h3>
                <div class="code-block">
                    <pre><code>let age = 18;
let name = "Alice";

// Comparison operators
console.log(age > 16);        // true
console.log(age < 21);        // true
console.log(age === 18);      // true (strict equality)
console.log(age == "18");     // true (loose equality)
console.log(age !== 20);      // true (not equal)

// Logical operators
console.log(age > 16 && age < 21);  // true (AND)
console.log(age < 16 || age > 65);  // false (OR)
console.log(!(age < 16));           // true (NOT)</code></pre>
                </div>
                <div class="live-demo">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <h4 style="margin-top: 0;">Comparisons</h4>
                                <div style="font-family: monospace; line-height: 1.6;">
                                    18 > 16 = <span style="color: #28a745;">true</span><br>
                                    18 < 21 = <span style="color: #28a745;">true</span><br>
                                    18 === 18 = <span style="color: #28a745;">true</span><br>
                                    18 == "18" = <span style="color: #28a745;">true</span><br>
                                    18 !== 20 = <span style="color: #28a745;">true</span>
                                </div>
                            </div>
                            <div>
                                <h4 style="margin-top: 0;">Logical Operators</h4>
                                <div style="font-family: monospace; line-height: 1.6;">
                                    true && true = <span style="color: #28a745;">true</span><br>
                                    true || false = <span style="color: #28a745;">true</span><br>
                                    !false = <span style="color: #28a745;">true</span><br>
                                    false && true = <span style="color: #dc3545;">false</span><br>
                                    false || false = <span style="color: #dc3545;">false</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ Booleans represent true/false values. Comparison and logical operators help make decisions in code.</p>
            </div>

            <!-- Example 9: Arrays Basics -->
            <div class="demo-container">
                <h3>Example 9: Introduction to Arrays</h3>
                <div class="code-block">
                    <pre><code>// Creating arrays
let fruits = ["apple", "banana", "orange"];
let numbers = [1, 2, 3, 4, 5];
let mixed = ["hello", 42, true];

// Accessing array elements (0-based indexing)
console.log(fruits[0]);  // "apple"
console.log(fruits[1]);  // "banana"
console.log(fruits[2]);  // "orange"

// Array properties and methods
console.log(fruits.length);     // 3
fruits.push("grape");           // Add to end
console.log(fruits);            // ["apple", "banana", "orange", "grape"]</code></pre>
                </div>
                <div class="live-demo">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <div style="margin-bottom: 15px;">
                            <strong>Array Visualization:</strong>
                            <div style="display: flex; gap: 10px; margin: 10px 0; align-items: center;">
                                <div style="background: #007bff; color: white; padding: 8px 12px; border-radius: 4px; font-family: monospace;">Index: 0<br>"apple"</div>
                                <div style="background: #28a745; color: white; padding: 8px 12px; border-radius: 4px; font-family: monospace;">Index: 1<br>"banana"</div>
                                <div style="background: #ffc107; color: black; padding: 8px 12px; border-radius: 4px; font-family: monospace;">Index: 2<br>"orange"</div>
                            </div>
                        </div>
                        <div>
                            <strong>Array Operations:</strong>
                            <div style="font-family: monospace; margin: 10px 0;">
                                fruits.length = <span style="color: #28a745;">3</span><br>
                                fruits[0] = <span style="color: #28a745;">"apple"</span><br>
                                fruits.push("grape") = <span style="color: #28a745;">["apple", "banana", "orange", "grape"]</span>
                            </div>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ Arrays store multiple values in order. Use square brackets and 0-based indexing to access elements.</p>
            </div>

            <!-- Example 10: Objects Basics -->
            <div class="demo-container">
                <h3>Example 10: Introduction to Objects</h3>
                <div class="code-block">
                    <pre><code>// Creating objects
let person = {
    name: "Alice",
    age: 25,
    city: "New York",
    isStudent: false
};

// Accessing object properties
console.log(person.name);        // "Alice"
console.log(person["age"]);      // 25

// Adding/modifying properties
person.email = "<EMAIL>";
person.age = 26;

console.log(person);</code></pre>
                </div>
                <div class="live-demo">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <div style="margin-bottom: 15px;">
                            <strong>Object Visualization:</strong>
                            <div style="background: white; border: 2px solid #007bff; border-radius: 8px; padding: 15px; margin: 10px 0;">
                                <div style="font-family: monospace; line-height: 1.8;">
                                    <div><span style="color: #007bff;">name:</span> "Alice"</div>
                                    <div><span style="color: #007bff;">age:</span> 25</div>
                                    <div><span style="color: #007bff;">city:</span> "New York"</div>
                                    <div><span style="color: #007bff;">isStudent:</span> false</div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <strong>Property Access:</strong>
                            <div style="font-family: monospace; margin: 10px 0;">
                                person.name = <span style="color: #28a745;">"Alice"</span><br>
                                person["age"] = <span style="color: #28a745;">25</span><br>
                                person.email = <span style="color: #28a745;">"<EMAIL>"</span> (added)
                            </div>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ Objects store related data as key-value pairs. Use dot notation or brackets to access properties.</p>
            </div>
        </section>

        <section class="examples" id="control-flow">
            <h2>Control Flow: Making Decisions and Loops</h2>
            <p>Now let's learn how to make decisions and repeat actions in JavaScript.</p>

            <!-- Example 11: If Statements -->
            <div class="demo-container">
                <h3>Example 11: If Statements (Making Decisions)</h3>
                <div class="code-block">
                    <pre><code>let age = 18;

if (age >= 18) {
    console.log("You can vote!");
} else {
    console.log("You cannot vote yet.");
}

// Multiple conditions
let score = 85;

if (score >= 90) {
    console.log("Grade: A");
} else if (score >= 80) {
    console.log("Grade: B");
} else if (score >= 70) {
    console.log("Grade: C");
} else {
    console.log("Grade: F");
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <div style="margin-bottom: 15px;">
                            <strong>Age Check:</strong>
                            <div style="display: flex; gap: 10px; margin: 10px 0; align-items: center;">
                                <input type="number" id="age-input" value="18" min="0" max="100" style="padding: 8px; border: 1px solid #ccc; border-radius: 4px; width: 80px;">
                                <button onclick="checkAge()" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Check Age</button>
                                <span id="age-result" style="font-weight: bold; color: #28a745;"></span>
                            </div>
                        </div>
                        <div>
                            <strong>Grade Calculator:</strong>
                            <div style="display: flex; gap: 10px; margin: 10px 0; align-items: center;">
                                <input type="number" id="score-input" value="85" min="0" max="100" style="padding: 8px; border: 1px solid #ccc; border-radius: 4px; width: 80px;">
                                <button onclick="calculateGrade()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Get Grade</button>
                                <span id="grade-result" style="font-weight: bold; color: #007bff;"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ If statements let your code make decisions based on conditions. Use else if for multiple conditions.</p>
            </div>

            <!-- Example 12: For Loops -->
            <div class="demo-container">
                <h3>Example 12: For Loops (Repeating Actions)</h3>
                <div class="code-block">
                    <pre><code>// Basic for loop
for (let i = 0; i < 5; i++) {
    console.log("Count: " + i);
}

// Loop through an array
let fruits = ["apple", "banana", "orange"];
for (let i = 0; i < fruits.length; i++) {
    console.log(fruits[i]);
}

// Modern for...of loop
for (let fruit of fruits) {
    console.log(fruit);
}</code></pre>
                </div>
                <div class="live-demo">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <div style="margin-bottom: 15px;">
                            <strong>Basic For Loop:</strong>
                            <div id="basic-loop-output" style="background: white; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0;">
                                Click "Run Loop" to see output
                            </div>
                            <button onclick="runBasicLoop()" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Run Loop</button>
                        </div>
                        <div>
                            <strong>Array Loop:</strong>
                            <div id="array-loop-output" style="background: white; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0;">
                                Click "Loop Array" to see output
                            </div>
                            <button onclick="runArrayLoop()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Loop Array</button>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ Loops repeat code multiple times. For loops are great for counting and iterating through arrays.</p>
            </div>

            <!-- Example 13: While Loops -->
            <div class="demo-container">
                <h3>Example 13: While Loops</h3>
                <div class="code-block">
                    <pre><code>// While loop
let count = 0;
while (count < 3) {
    console.log("Count is: " + count);
    count++;
}

// Do-while loop (runs at least once)
let number = 0;
do {
    console.log("Number: " + number);
    number++;
} while (number < 3);</code></pre>
                </div>
                <div class="live-demo">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <strong>While Loop:</strong>
                                <div id="while-output" style="background: white; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; min-height: 60px;">
                                    Ready to run
                                </div>
                                <button onclick="runWhileLoop()" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Run While</button>
                            </div>
                            <div>
                                <strong>Do-While Loop:</strong>
                                <div id="do-while-output" style="background: white; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; min-height: 60px;">
                                    Ready to run
                                </div>
                                <button onclick="runDoWhileLoop()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Run Do-While</button>
                            </div>
                        </div>
                    </div>
                </div>
                <p class="explanation">✅ While loops continue as long as a condition is true. Do-while loops always run at least once.</p>
            </div>
        </section>

        <section class="advanced" id="functions">
            <h2>Functions: Reusable Code Blocks</h2>
            <p>Functions are the building blocks of JavaScript applications. Let's master them step by step.</p>
            
            <div class="demo-container">
                <h3>Function Types</h3>
                <div class="code-block">
                    <pre><code>// Function Declaration
function greet(name) {
    return `Hello, ${name}!`;
}

// Function Expression
const add = function(a, b) {
    return a + b;
};

// Arrow Functions
const multiply = (a, b) => a * b;
const square = x => x * x;
const sayHello = () => console.log("Hello!");

// Higher-order Functions
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
const evens = numbers.filter(n => n % 2 === 0);
const sum = numbers.reduce((acc, n) => acc + n, 0);

// Default Parameters
function createUser(name, age = 18, role = 'user') {
    return { name, age, role };
}

// Rest Parameters
function sum(...numbers) {
    return numbers.reduce((total, num) => total + num, 0);
}

// Destructuring Parameters
function displayUser({ name, age, email }) {
    console.log(`${name} (${age}) - ${email}`);
}</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Function Playground</h3>
                <div class="function-demo">
                    <div class="js-playground">
                        <div>
                            <h4>Function Examples:</h4>
                            <textarea class="code-editor" id="function-editor">// Try different function types
const calculator = {
    add: (a, b) => a + b,
    subtract: (a, b) => a - b,
    multiply: (a, b) => a * b,
    divide: (a, b) => b !== 0 ? a / b : 'Cannot divide by zero'
};

console.log(calculator.add(5, 3));
console.log(calculator.multiply(4, 7));

// Array methods
const fruits = ['apple', 'banana', 'orange'];
console.log(fruits.map(fruit => fruit.toUpperCase()));</textarea>
                            <button onclick="runFunctionCode()">Run Code</button>
                        </div>
                        <div>
                            <h4>Console Output:</h4>
                            <div class="console-output" id="function-output"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="advanced" id="objects">
            <h2>Objects and Arrays</h2>
            <p>Objects and arrays are fundamental data structures in JavaScript for organizing and manipulating data.</p>
            
            <div class="demo-container">
                <h3>Object Operations</h3>
                <div class="code-block">
                    <pre><code>// Object creation and manipulation
const person = {
    name: "Alice",
    age: 30,
    city: "New York",
    hobbies: ["reading", "coding", "hiking"]
};

// Accessing properties
console.log(person.name);        // Dot notation
console.log(person["age"]);      // Bracket notation

// Adding/modifying properties
person.email = "<EMAIL>";
person.age = 31;

// Object methods
const user = {
    firstName: "John",
    lastName: "Doe",
    fullName() {
        return `${this.firstName} ${this.lastName}`;
    },
    greet: function() {
        return `Hello, I'm ${this.fullName()}`;
    }
};

// Object destructuring
const { name, age, city } = person;
const { firstName: fName, lastName: lName } = user;

// Object.keys, Object.values, Object.entries
console.log(Object.keys(person));
console.log(Object.values(person));
console.log(Object.entries(person));</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Array Methods</h3>
                <div class="code-block">
                    <pre><code>const numbers = [1, 2, 3, 4, 5];
const fruits = ["apple", "banana", "orange"];

// Array methods
numbers.push(6);                 // Add to end
numbers.unshift(0);              // Add to beginning
numbers.pop();                   // Remove from end
numbers.shift();                 // Remove from beginning

// Functional array methods
const doubled = numbers.map(n => n * 2);
const evens = numbers.filter(n => n % 2 === 0);
const sum = numbers.reduce((acc, n) => acc + n, 0);
const found = numbers.find(n => n > 3);
const hasEven = numbers.some(n => n % 2 === 0);
const allPositive = numbers.every(n => n > 0);

// Array destructuring
const [first, second, ...rest] = numbers;

// Spread operator
const combined = [...numbers, ...fruits];
const copy = [...numbers];</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Object & Array Playground</h3>
                <div class="object-demo">
                    <div class="js-playground">
                        <div>
                            <h4>Try Objects & Arrays:</h4>
                            <textarea class="code-editor" id="object-editor">// Create and manipulate objects and arrays
const students = [
    { name: "Alice", grade: 85, subject: "Math" },
    { name: "Bob", grade: 92, subject: "Science" },
    { name: "Charlie", grade: 78, subject: "Math" }
];

// Find all Math students
const mathStudents = students.filter(s => s.subject === "Math");
console.log("Math students:", mathStudents);

// Calculate average grade
const avgGrade = students.reduce((sum, s) => sum + s.grade, 0) / students.length;
console.log("Average grade:", avgGrade);

// Get student names
const names = students.map(s => s.name);
console.log("Student names:", names);</textarea>
                            <button onclick="runObjectCode()">Run Code</button>
                        </div>
                        <div>
                            <h4>Console Output:</h4>
                            <div class="console-output" id="object-output"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="best-practices" id="control-flow">
            <h2>Control Flow</h2>
            <p>Control flow statements allow you to control the execution of your code based on conditions and loops.</p>
            
            <div class="demo-container">
                <h3>Conditional Statements</h3>
                <div class="code-block">
                    <pre><code>// If statements
const score = 85;

if (score >= 90) {
    console.log("A grade");
} else if (score >= 80) {
    console.log("B grade");
} else if (score >= 70) {
    console.log("C grade");
} else {
    console.log("Need improvement");
}

// Ternary operator
const status = score >= 60 ? "Pass" : "Fail";

// Switch statement
const day = "Monday";
switch (day) {
    case "Monday":
        console.log("Start of work week");
        break;
    case "Friday":
        console.log("TGIF!");
        break;
    case "Saturday":
    case "Sunday":
        console.log("Weekend!");
        break;
    default:
        console.log("Regular day");
}

// Nullish coalescing operator
const username = null;
const displayName = username ?? "Guest";

// Optional chaining
const user = { profile: { name: "John" } };
console.log(user.profile?.name);        // "John"
console.log(user.settings?.theme);     // undefined</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Loops</h3>
                <div class="code-block">
                    <pre><code>// For loop
for (let i = 0; i < 5; i++) {
    console.log(`Iteration ${i}`);
}

// For...of loop (for arrays)
const colors = ["red", "green", "blue"];
for (const color of colors) {
    console.log(color);
}

// For...in loop (for objects)
const person = { name: "Alice", age: 30, city: "NYC" };
for (const key in person) {
    console.log(`${key}: ${person[key]}`);
}

// While loop
let count = 0;
while (count < 3) {
    console.log(`Count: ${count}`);
    count++;
}

// Do...while loop
let num = 0;
do {
    console.log(`Number: ${num}`);
    num++;
} while (num < 3);

// Array iteration methods
const numbers = [1, 2, 3, 4, 5];
numbers.forEach((num, index) => {
    console.log(`Index ${index}: ${num}`);
});</code></pre>
                </div>
            </div>
        </section>

        <section class="modern-features" id="modern-js">
            <h2>Modern JavaScript Features</h2>
            <p>ES6+ introduced many powerful features that make JavaScript more expressive and easier to work with.</p>
            
            <div class="demo-container">
                <h3>ES6+ Features</h3>
                <div class="code-block">
                    <pre><code>// Destructuring
const [a, b, ...rest] = [1, 2, 3, 4, 5];
const { name, age, ...otherProps } = { name: "John", age: 30, city: "NYC", job: "Developer" };

// Spread operator
const arr1 = [1, 2, 3];
const arr2 = [4, 5, 6];
const combined = [...arr1, ...arr2];

const obj1 = { a: 1, b: 2 };
const obj2 = { c: 3, d: 4 };
const merged = { ...obj1, ...obj2 };

// Template literals
const name = "Alice";
const age = 25;
const message = `Hello, my name is ${name} and I'm ${age} years old.`;

// Classes
class Person {
    constructor(name, age) {
        this.name = name;
        this.age = age;
    }
    
    greet() {
        return `Hello, I'm ${this.name}`;
    }
    
    static species() {
        return "Homo sapiens";
    }
}

class Student extends Person {
    constructor(name, age, grade) {
        super(name, age);
        this.grade = grade;
    }
    
    study() {
        return `${this.name} is studying`;
    }
}

// Modules (ES6 modules)
// export const PI = 3.14159;
// export default function calculate() { ... }
// import calculate, { PI } from './math.js';</code></pre>
                </div>
            </div>

            <div class="demo-container">
                <h3>Modern JavaScript Playground</h3>
                <div class="js-playground">
                    <div>
                        <h4>Try Modern Features:</h4>
                        <textarea class="code-editor" id="modern-editor">// Try modern JavaScript features
class Calculator {
    constructor() {
        this.history = [];
    }
    
    add(a, b) {
        const result = a + b;
        this.history.push(`${a} + ${b} = ${result}`);
        return result;
    }
    
    getHistory() {
        return this.history;
    }
}

const calc = new Calculator();
console.log(calc.add(5, 3));
console.log(calc.add(10, 7));
console.log("History:", calc.getHistory());

// Destructuring and spread
const numbers = [1, 2, 3, 4, 5];
const [first, second, ...others] = numbers;
console.log("First:", first, "Others:", others);</textarea>
                        <button onclick="runModernCode()">Run Code</button>
                    </div>
                    <div>
                        <h4>Console Output:</h4>
                        <div class="console-output" id="modern-output"></div>
                    </div>
                </div>
            </div>
        </section>

        <section class="advanced-examples" id="advanced-examples">
            <h2>Advanced JavaScript & Real-World Examples (21-35+)</h2>
            <p>Let's complete our JavaScript journey with advanced concepts and build real applications.</p>

            <!-- Complete Todo App Example -->
            <div class="demo-container">
                <h3>Example 21-35: Complete Todo App (Everything Together!)</h3>
                <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #ddd; margin: 10px 0;">
                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <input type="text" id="todo-input" placeholder="Enter a task..." style="flex: 1; padding: 10px; border: 1px solid #ccc; border-radius: 4px; font-size: 16px;">
                        <button onclick="addTodo()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-size: 16px;">Add Task</button>
                    </div>
                    <div id="todo-list" style="max-height: 300px; overflow-y: auto; margin-bottom: 15px;">
                        <!-- Todos will be added here -->
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 15px; border-top: 1px solid #eee;">
                        <span id="todo-count" style="color: #666;">0 tasks</span>
                        <div>
                            <button onclick="showAll()" id="show-all" style="background: #28a745; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin: 0 2px;">All</button>
                            <button onclick="showActive()" id="show-active" style="background: #6c757d; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin: 0 2px;">Active</button>
                            <button onclick="showCompleted()" id="show-completed" style="background: #6c757d; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin: 0 2px;">Completed</button>
                            <button onclick="clearCompleted()" style="background: #dc3545; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-left: 10px;">Clear Completed</button>
                        </div>
                    </div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <h4>JavaScript Concepts Used:</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 10px;">
                        <div>✅ DOM Manipulation</div>
                        <div>✅ Event Handling</div>
                        <div>✅ Local Storage</div>
                        <div>✅ Array Methods</div>
                        <div>✅ Object-Oriented Programming</div>
                        <div>✅ Modern ES6+ Features</div>
                        <div>✅ Error Handling</div>
                        <div>✅ JSON Operations</div>
                    </div>
                </div>
                <p class="explanation">🎉 <strong>Congratulations!</strong> This todo app demonstrates how all JavaScript concepts work together in a real application!</p>
            </div>

            <!-- JavaScript Learning Journey Summary -->
            <div class="demo-container" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; text-align: center;">
                <h3 style="color: white; margin-top: 0;">🎉 Your JavaScript Journey: From Zero to Hero!</h3>
                <p style="font-size: 1.1em; margin: 20px 0;">You've mastered 35+ JavaScript concepts in logical progression:</p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; text-align: left;">
                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #fff; margin-top: 0;">🔰 Foundation (1-10)</h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li>First JavaScript code</li>
                            <li>Variables (let, const)</li>
                            <li>Data types & operations</li>
                            <li>Arrays & objects basics</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #fff; margin-top: 0;">🔄 Control Flow (11-20)</h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li>If statements & loops</li>
                            <li>Functions & parameters</li>
                            <li>Arrow functions</li>
                            <li>DOM manipulation & events</li>
                        </ul>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #fff; margin-top: 0;">🚀 Advanced (21-35)</h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li>Async/await & promises</li>
                            <li>Modern ES6+ features</li>
                            <li>APIs & data handling</li>
                            <li>Complete applications</li>
                        </ul>
                    </div>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin-top: 30px;">
                    <h4 style="color: #fff; margin-top: 0;">🎯 What You Can Build Now:</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px;">
                        <div>✅ Interactive websites</div>
                        <div>✅ Todo applications</div>
                        <div>✅ API integrations</div>
                        <div>✅ Modern web apps</div>
                        <div>✅ Data manipulation</div>
                        <div>✅ User interfaces</div>
                    </div>
                </div>

                <p style="font-size: 1.2em; margin-top: 30px; font-weight: bold;">You're now ready to build dynamic, interactive web applications with JavaScript!</p>
            </div>
        </section>

        <section class="resources" id="resources">
            <h2>Additional Resources</h2>
            <div class="resource-grid">
                <div class="resource-card">
                    <h3>📖 Official Documentation</h3>
                    <ul>
                        <li><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript" target="_blank">MDN JavaScript Reference</a></li>
                        <li><a href="https://tc39.es/ecma262/" target="_blank">ECMAScript Specification</a></li>
                        <li><a href="https://javascript.info/" target="_blank">JavaScript.info</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>🛠️ Tools & Playgrounds</h3>
                    <ul>
                        <li><a href="https://codepen.io/" target="_blank">CodePen</a></li>
                        <li><a href="https://jsfiddle.net/" target="_blank">JSFiddle</a></li>
                        <li><a href="https://replit.com/" target="_blank">Replit</a></li>
                    </ul>
                </div>
                
                <div class="resource-card">
                    <h3>📚 Learning Resources</h3>
                    <ul>
                        <li><a href="https://eloquentjavascript.net/" target="_blank">Eloquent JavaScript</a></li>
                        <li><a href="https://github.com/getify/You-Dont-Know-JS" target="_blank">You Don't Know JS</a></li>
                        <li><a href="https://javascript30.com/" target="_blank">JavaScript30</a></li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <script src="assets/js/manual-scripts.js"></script>
    <script>
        // Console simulation functions
        function createConsole(outputId) {
            const output = document.getElementById(outputId);
            return {
                log: (...args) => {
                    const message = args.map(arg => 
                        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                    ).join(' ');
                    output.textContent += message + '\n';
                },
                clear: () => {
                    output.textContent = '';
                }
            };
        }
        
        function runVariableCode() {
            const code = document.getElementById('variable-editor').value;
            const console = createConsole('variable-output');
            console.clear();
            
            try {
                // Create a function to execute the code with our custom console
                const func = new Function('console', code);
                func(console);
            } catch (error) {
                console.log('Error: ' + error.message);
            }
        }
        
        function runFunctionCode() {
            const code = document.getElementById('function-editor').value;
            const console = createConsole('function-output');
            console.clear();
            
            try {
                const func = new Function('console', code);
                func(console);
            } catch (error) {
                console.log('Error: ' + error.message);
            }
        }
        
        function runObjectCode() {
            const code = document.getElementById('object-editor').value;
            const console = createConsole('object-output');
            console.clear();
            
            try {
                const func = new Function('console', code);
                func(console);
            } catch (error) {
                console.log('Error: ' + error.message);
            }
        }
        
        function runModernCode() {
            const code = document.getElementById('modern-editor').value;
            const console = createConsole('modern-output');
            console.clear();
            
            try {
                const func = new Function('console', code);
                func(console);
            } catch (error) {
                console.log('Error: ' + error.message);
            }
        }
        
        // Additional JavaScript Demo Functions for Examples 1-35

        // Todo App (Complete Example 21-35)
        let todos = [];
        let currentFilter = 'all';

        // Load todos from localStorage
        try {
            todos = JSON.parse(localStorage.getItem('todos')) || [];
        } catch (e) {
            todos = [];
        }

        function addTodo() {
            const input = document.getElementById('todo-input');
            if (!input) return;

            const text = input.value.trim();

            if (text) {
                const todo = {
                    id: Date.now(),
                    text: text,
                    completed: false,
                    createdAt: new Date().toISOString()
                };

                todos.push(todo);
                input.value = '';
                saveTodos();
                renderTodos();
            }
        }

        function toggleTodo(id) {
            todos = todos.map(todo =>
                todo.id === id ? { ...todo, completed: !todo.completed } : todo
            );
            saveTodos();
            renderTodos();
        }

        function deleteTodo(id) {
            todos = todos.filter(todo => todo.id !== id);
            saveTodos();
            renderTodos();
        }

        function clearCompleted() {
            todos = todos.filter(todo => !todo.completed);
            saveTodos();
            renderTodos();
        }

        function showAll() {
            currentFilter = 'all';
            updateFilterButtons();
            renderTodos();
        }

        function showActive() {
            currentFilter = 'active';
            updateFilterButtons();
            renderTodos();
        }

        function showCompleted() {
            currentFilter = 'completed';
            updateFilterButtons();
            renderTodos();
        }

        function updateFilterButtons() {
            const showAll = document.getElementById('show-all');
            const showActive = document.getElementById('show-active');
            const showCompleted = document.getElementById('show-completed');

            if (showAll) showAll.style.background = currentFilter === 'all' ? '#28a745' : '#6c757d';
            if (showActive) showActive.style.background = currentFilter === 'active' ? '#28a745' : '#6c757d';
            if (showCompleted) showCompleted.style.background = currentFilter === 'completed' ? '#28a745' : '#6c757d';
        }

        function getFilteredTodos() {
            switch (currentFilter) {
                case 'active':
                    return todos.filter(todo => !todo.completed);
                case 'completed':
                    return todos.filter(todo => todo.completed);
                default:
                    return todos;
            }
        }

        function renderTodos() {
            const todoList = document.getElementById('todo-list');
            if (!todoList) return;

            const filteredTodos = getFilteredTodos();

            if (filteredTodos.length === 0) {
                todoList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">No tasks to show</p>';
            } else {
                todoList.innerHTML = filteredTodos.map(todo => `
                    <div style="display: flex; align-items: center; padding: 10px; border-bottom: 1px solid #eee; ${todo.completed ? 'opacity: 0.6;' : ''}">
                        <input type="checkbox" ${todo.completed ? 'checked' : ''}
                               onchange="toggleTodo(${todo.id})"
                               style="margin-right: 10px;">
                        <span style="flex: 1; ${todo.completed ? 'text-decoration: line-through;' : ''}">${todo.text}</span>
                        <button onclick="deleteTodo(${todo.id})"
                                style="background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer;">Delete</button>
                    </div>
                `).join('');
            }

            updateTodoCount();
        }

        function updateTodoCount() {
            const activeCount = todos.filter(todo => !todo.completed).length;
            const totalCount = todos.length;
            const countElement = document.getElementById('todo-count');
            if (countElement) {
                countElement.textContent = `${activeCount} active, ${totalCount} total`;
            }
        }

        function saveTodos() {
            try {
                localStorage.setItem('todos', JSON.stringify(todos));
            } catch (e) {
                console.error('Failed to save todos:', e);
            }
        }

        // Initialize with default output
        document.addEventListener('DOMContentLoaded', function() {
            runVariableCode();
            runFunctionCode();
            runObjectCode();
            runModernCode();

            // Initialize todo app
            renderTodos();
            updateFilterButtons();

            // Add enter key support for todo input
            const todoInput = document.getElementById('todo-input');
            if (todoInput) {
                todoInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        addTodo();
                    }
                });
            }
        });
    </script>
</body>
</html>
