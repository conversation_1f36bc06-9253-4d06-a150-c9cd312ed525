<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Basics - Web Development Manual</title>
    <meta name="description" content="Learn Three.js fundamentals with interactive 3D examples and practical tutorials">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="assets/css/manual-styles.css">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Fira+Code:wght@300;400;500&display=swap" rel="stylesheet">
    
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
    
    <!-- GSAP for UI animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .code-font { font-family: 'Fira Code', monospace; }
        
        .threejs-canvas {
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .control-panel {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            padding: 1rem;
            color: white;
            position: absolute;
            top: 1rem;
            right: 1rem;
            z-index: 10;
        }
        
        .control-panel button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            margin: 0.25rem;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .control-panel button:hover {
            background: #2563eb;
        }
        
        .stats-panel {
            position: absolute;
            bottom: 1rem;
            left: 1rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Fira Code', monospace;
            font-size: 0.875rem;
            z-index: 10;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- Navigation -->
    <nav class="manual-nav fixed top-0 left-0 right-0 z-50 px-6 py-4">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="index.html" class="text-2xl font-bold text-blue-600">WebDev Manual</a>
                <span class="text-sm text-gray-500">Three.js Basics</span>
            </div>
            
            <div class="hidden md:flex items-center space-x-6">
                <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
                <a href="#introduction" class="text-gray-700 hover:text-blue-600 transition-colors">Introduction</a>
                <a href="#setup" class="text-gray-700 hover:text-blue-600 transition-colors">Setup</a>
                <a href="#examples" class="text-gray-700 hover:text-blue-600 transition-colors">Examples</a>
                <a href="#advanced" class="text-gray-700 hover:text-blue-600 transition-colors">Advanced</a>
                <button id="theme-toggle" class="p-2 rounded-lg bg-gray-200 hover:bg-gray-300 transition-colors">
                    🌙
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-24 pb-16 px-6">
        <div class="max-w-6xl mx-auto text-center">
            <h1 class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-red-600 to-blue-600 bg-clip-text text-transparent">
                Three.js Basics
            </h1>
            
            <p class="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto">
                Learn Three.js - the most popular JavaScript 3D library. Create stunning 3D graphics, 
                animations, and interactive experiences right in the browser.
            </p>
            
            <div class="flex flex-wrap justify-center gap-4 mb-8">
                <span class="bg-red-600 text-white px-4 py-2 rounded-full text-sm font-semibold">WebGL</span>
                <span class="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Cross-Platform</span>
                <span class="bg-green-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Hardware Accelerated</span>
                <span class="bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Open Source</span>
            </div>
            
            <!-- Hero 3D Demo -->
            <div class="relative max-w-4xl mx-auto">
                <div id="hero-threejs" class="threejs-container h-96"></div>
                <div class="control-panel">
                    <div class="text-sm mb-2">Hero Demo Controls</div>
                    <button onclick="toggleHeroAnimation()">Toggle Animation</button>
                    <button onclick="changeHeroGeometry()">Change Shape</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Introduction -->
    <section id="introduction" class="py-16 px-6 bg-white">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">What is Three.js?</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h3 class="text-2xl font-semibold mb-6">JavaScript 3D Library</h3>
                    <ul class="space-y-4 text-lg text-gray-700">
                        <li class="flex items-start">
                            <span class="text-red-500 mr-3">✓</span>
                            <span><strong>WebGL abstraction</strong> - Makes 3D graphics accessible</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-red-500 mr-3">✓</span>
                            <span><strong>Cross-platform</strong> - Works on desktop, mobile, and VR</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-red-500 mr-3">✓</span>
                            <span><strong>Hardware accelerated</strong> - Leverages GPU for performance</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-red-500 mr-3">✓</span>
                            <span><strong>Rich ecosystem</strong> - Plugins, loaders, and tools</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-red-500 mr-3">✓</span>
                            <span><strong>Active community</strong> - Regular updates and support</span>
                        </li>
                    </ul>
                    
                    <div class="mt-8">
                        <h4 class="text-xl font-semibold mb-4">Installation</h4>
                        <div class="code-block relative">
                            <div class="text-comment">// CDN (Quick Start)</div>
                            <div>&lt;<span class="keyword">script</span> <span class="string">src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"</span>&gt;&lt;/<span class="keyword">script</span>&gt;</div>
                            <br>
                            <div class="text-comment">// NPM (Production)</div>
                            <div><span class="keyword">npm install</span> <span class="string">three</span></div>
                            <br>
                            <div class="text-comment">// ES6 Import</div>
                            <div><span class="keyword">import</span> * <span class="keyword">as</span> <span class="function">THREE</span> <span class="keyword">from</span> <span class="string">'three'</span>;</div>
                        </div>
                    </div>
                </div>
                
                <div class="relative">
                    <div id="intro-threejs" class="threejs-container h-96"></div>
                    <div class="stats-panel">
                        <div>FPS: <span id="intro-fps">60</span></div>
                        <div>Triangles: <span id="intro-triangles">12</span></div>
                        <div>Vertices: <span id="intro-vertices">8</span></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Setup -->
    <section id="setup" class="py-16 px-6">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">Basic Setup</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                    <h3 class="text-2xl font-semibold mb-6">Essential Components</h3>
                    <div class="space-y-6">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">1. Scene</h4>
                            <p class="text-blue-700">Container for all 3D objects, lights, and cameras</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">2. Camera</h4>
                            <p class="text-green-700">Defines the viewpoint and perspective</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">3. Renderer</h4>
                            <p class="text-purple-700">Draws the 3D scene to the canvas</p>
                        </div>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">4. Mesh</h4>
                            <p class="text-orange-700">Combines geometry and material to create objects</p>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-xl font-semibold mb-4">Basic Three.js Setup</h4>
                    <div class="code-block relative">
                        <div class="text-comment">// 1. Create scene, camera, renderer</div>
                        <div><span class="keyword">const</span> <span class="function">scene</span> = <span class="keyword">new</span> <span class="function">THREE.Scene</span>();</div>
                        <div><span class="keyword">const</span> <span class="function">camera</span> = <span class="keyword">new</span> <span class="function">THREE.PerspectiveCamera</span>(</div>
                        <div>&nbsp;&nbsp;<span class="number">75</span>, <span class="text-comment">// FOV</span></div>
                        <div>&nbsp;&nbsp;<span class="function">window.innerWidth</span> / <span class="function">window.innerHeight</span>, <span class="text-comment">// Aspect</span></div>
                        <div>&nbsp;&nbsp;<span class="number">0.1</span>, <span class="text-comment">// Near</span></div>
                        <div>&nbsp;&nbsp;<span class="number">1000</span> <span class="text-comment">// Far</span></div>
                        <div>);</div>
                        <br>
                        <div><span class="keyword">const</span> <span class="function">renderer</span> = <span class="keyword">new</span> <span class="function">THREE.WebGLRenderer</span>();</div>
                        <div><span class="function">renderer</span>.<span class="function">setSize</span>(<span class="function">window.innerWidth</span>, <span class="function">window.innerHeight</span>);</div>
                        <div><span class="function">document.body</span>.<span class="function">appendChild</span>(<span class="function">renderer.domElement</span>);</div>
                        <br>
                        <div class="text-comment">// 2. Create geometry and material</div>
                        <div><span class="keyword">const</span> <span class="function">geometry</span> = <span class="keyword">new</span> <span class="function">THREE.BoxGeometry</span>(<span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>);</div>
                        <div><span class="keyword">const</span> <span class="function">material</span> = <span class="keyword">new</span> <span class="function">THREE.MeshBasicMaterial</span>({</div>
                        <div>&nbsp;&nbsp;<span class="keyword">color</span>: <span class="string">0x00ff00</span></div>
                        <div>});</div>
                        <br>
                        <div class="text-comment">// 3. Create mesh and add to scene</div>
                        <div><span class="keyword">const</span> <span class="function">cube</span> = <span class="keyword">new</span> <span class="function">THREE.Mesh</span>(<span class="function">geometry</span>, <span class="function">material</span>);</div>
                        <div><span class="function">scene</span>.<span class="function">add</span>(<span class="function">cube</span>);</div>
                        <br>
                        <div class="text-comment">// 4. Position camera and render</div>
                        <div><span class="function">camera.position.z</span> = <span class="number">5</span>;</div>
                        <div><span class="function">renderer</span>.<span class="function">render</span>(<span class="function">scene</span>, <span class="function">camera</span>);</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Examples -->
    <section id="examples" class="py-16 px-6 bg-white">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">Interactive Examples</h2>
            
            <!-- Basic Cube -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">1. Rotating Cube</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">// Animation loop</div>
                            <div><span class="keyword">function</span> <span class="function">animate</span>() {</div>
                            <div>&nbsp;&nbsp;<span class="function">requestAnimationFrame</span>(<span class="function">animate</span>);</div>
                            <div>&nbsp;&nbsp;</div>
                            <div>&nbsp;&nbsp;<span class="text-comment">// Rotate the cube</span></div>
                            <div>&nbsp;&nbsp;<span class="function">cube.rotation.x</span> += <span class="number">0.01</span>;</div>
                            <div>&nbsp;&nbsp;<span class="function">cube.rotation.y</span> += <span class="number">0.01</span>;</div>
                            <div>&nbsp;&nbsp;</div>
                            <div>&nbsp;&nbsp;<span class="function">renderer</span>.<span class="function">render</span>(<span class="function">scene</span>, <span class="function">camera</span>);</div>
                            <div>}</div>
                            <br>
                            <div><span class="function">animate</span>();</div>
                        </div>
                    </div>
                    <div class="relative">
                        <div id="cube-demo" class="threejs-container h-80"></div>
                        <div class="control-panel">
                            <button onclick="toggleCubeAnimation()">Toggle Animation</button>
                            <button onclick="changeCubeColor()">Change Color</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Multiple Geometries -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">2. Multiple Geometries</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">// Different geometries</div>
                            <div><span class="keyword">const</span> <span class="function">sphereGeometry</span> = <span class="keyword">new</span> <span class="function">THREE.SphereGeometry</span>(<span class="number">0.5</span>, <span class="number">32</span>, <span class="number">32</span>);</div>
                            <div><span class="keyword">const</span> <span class="function">cylinderGeometry</span> = <span class="keyword">new</span> <span class="function">THREE.CylinderGeometry</span>(<span class="number">0.5</span>, <span class="number">0.5</span>, <span class="number">1</span>);</div>
                            <div><span class="keyword">const</span> <span class="function">coneGeometry</span> = <span class="keyword">new</span> <span class="function">THREE.ConeGeometry</span>(<span class="number">0.5</span>, <span class="number">1</span>, <span class="number">8</span>);</div>
                            <br>
                            <div class="text-comment">// Different materials</div>
                            <div><span class="keyword">const</span> <span class="function">basicMaterial</span> = <span class="keyword">new</span> <span class="function">THREE.MeshBasicMaterial</span>({<span class="keyword">color</span>: <span class="string">0xff0000</span>});</div>
                            <div><span class="keyword">const</span> <span class="function">lambertMaterial</span> = <span class="keyword">new</span> <span class="function">THREE.MeshLambertMaterial</span>({<span class="keyword">color</span>: <span class="string">0x00ff00</span>});</div>
                            <div><span class="keyword">const</span> <span class="function">phongMaterial</span> = <span class="keyword">new</span> <span class="function">THREE.MeshPhongMaterial</span>({<span class="keyword">color</span>: <span class="string">0x0000ff</span>});</div>
                            <br>
                            <div class="text-comment">// Position objects</div>
                            <div><span class="function">sphere.position.x</span> = <span class="number">-2</span>;</div>
                            <div><span class="function">cylinder.position.x</span> = <span class="number">0</span>;</div>
                            <div><span class="function">cone.position.x</span> = <span class="number">2</span>;</div>
                        </div>
                    </div>
                    <div class="relative">
                        <div id="geometries-demo" class="threejs-container h-80"></div>
                        <div class="control-panel">
                            <button onclick="toggleGeometriesAnimation()">Toggle Animation</button>
                            <button onclick="addRandomGeometry()">Add Random</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lighting -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">3. Lighting Effects</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">// Add lighting</div>
                            <div><span class="keyword">const</span> <span class="function">ambientLight</span> = <span class="keyword">new</span> <span class="function">THREE.AmbientLight</span>(<span class="string">0x404040</span>, <span class="number">0.4</span>);</div>
                            <div><span class="function">scene</span>.<span class="function">add</span>(<span class="function">ambientLight</span>);</div>
                            <br>
                            <div><span class="keyword">const</span> <span class="function">directionalLight</span> = <span class="keyword">new</span> <span class="function">THREE.DirectionalLight</span>(<span class="string">0xffffff</span>, <span class="number">0.8</span>);</div>
                            <div><span class="function">directionalLight.position</span>.<span class="function">set</span>(<span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>);</div>
                            <div><span class="function">scene</span>.<span class="function">add</span>(<span class="function">directionalLight</span>);</div>
                            <br>
                            <div class="text-comment">// Use materials that respond to light</div>
                            <div><span class="keyword">const</span> <span class="function">material</span> = <span class="keyword">new</span> <span class="function">THREE.MeshPhongMaterial</span>({</div>
                            <div>&nbsp;&nbsp;<span class="keyword">color</span>: <span class="string">0x00ff00</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">shininess</span>: <span class="number">100</span></div>
                            <div>});</div>
                        </div>
                    </div>
                    <div class="relative">
                        <div id="lighting-demo" class="threejs-container h-80"></div>
                        <div class="control-panel">
                            <button onclick="toggleLightAnimation()">Toggle Light</button>
                            <button onclick="changeLightColor()">Change Color</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Advanced -->
    <section id="advanced" class="py-16 px-6">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-4xl font-bold text-center mb-12">Advanced Features</h2>
            
            <!-- Orbit Controls -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">4. Orbit Controls</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">// Add orbit controls</div>
                            <div><span class="keyword">import</span> { <span class="function">OrbitControls</span> } <span class="keyword">from</span> <span class="string">'three/examples/jsm/controls/OrbitControls.js'</span>;</div>
                            <br>
                            <div><span class="keyword">const</span> <span class="function">controls</span> = <span class="keyword">new</span> <span class="function">OrbitControls</span>(<span class="function">camera</span>, <span class="function">renderer.domElement</span>);</div>
                            <br>
                            <div class="text-comment">// Configure controls</div>
                            <div><span class="function">controls.enableDamping</span> = <span class="keyword">true</span>;</div>
                            <div><span class="function">controls.dampingFactor</span> = <span class="number">0.05</span>;</div>
                            <div><span class="function">controls.enableZoom</span> = <span class="keyword">true</span>;</div>
                            <div><span class="function">controls.enablePan</span> = <span class="keyword">true</span>;</div>
                            <br>
                            <div class="text-comment">// Update in animation loop</div>
                            <div><span class="function">controls</span>.<span class="function">update</span>();</div>
                        </div>
                        <p class="text-gray-600 mt-4">
                            <strong>Mouse Controls:</strong><br>
                            • Left click + drag: Rotate<br>
                            • Right click + drag: Pan<br>
                            • Scroll: Zoom
                        </p>
                    </div>
                    <div class="relative">
                        <div id="controls-demo" class="threejs-container h-80"></div>
                        <div class="control-panel">
                            <div class="text-sm mb-2">Interactive Controls</div>
                            <div class="text-xs">Click and drag to rotate</div>
                            <div class="text-xs">Right-click to pan</div>
                            <div class="text-xs">Scroll to zoom</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Textures -->
            <div class="mb-16">
                <h3 class="text-2xl font-semibold mb-6">5. Textures and Materials</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <div class="code-block relative">
                            <div class="text-comment">// Load texture</div>
                            <div><span class="keyword">const</span> <span class="function">textureLoader</span> = <span class="keyword">new</span> <span class="function">THREE.TextureLoader</span>();</div>
                            <div><span class="keyword">const</span> <span class="function">texture</span> = <span class="function">textureLoader</span>.<span class="function">load</span>(<span class="string">'path/to/texture.jpg'</span>);</div>
                            <br>
                            <div class="text-comment">// Apply to material</div>
                            <div><span class="keyword">const</span> <span class="function">material</span> = <span class="keyword">new</span> <span class="function">THREE.MeshPhongMaterial</span>({</div>
                            <div>&nbsp;&nbsp;<span class="keyword">map</span>: <span class="function">texture</span></div>
                            <div>});</div>
                            <br>
                            <div class="text-comment">// Different material types</div>
                            <div><span class="keyword">const</span> <span class="function">standardMaterial</span> = <span class="keyword">new</span> <span class="function">THREE.MeshStandardMaterial</span>({</div>
                            <div>&nbsp;&nbsp;<span class="keyword">color</span>: <span class="string">0xffffff</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">metalness</span>: <span class="number">0.5</span>,</div>
                            <div>&nbsp;&nbsp;<span class="keyword">roughness</span>: <span class="number">0.1</span></div>
                            <div>});</div>
                        </div>
                    </div>
                    <div class="relative">
                        <div id="materials-demo" class="threejs-container h-80"></div>
                        <div class="control-panel">
                            <button onclick="changeMaterialType()">Change Material</button>
                            <button onclick="toggleWireframe()">Toggle Wireframe</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Resources -->
    <section class="py-16 px-6 bg-gray-900 text-white">
        <div class="max-w-6xl mx-auto text-center">
            <h2 class="text-4xl font-bold mb-8">Learn More</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div class="bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">Official Documentation</h3>
                    <p class="text-gray-300 mb-4">Complete Three.js documentation and examples</p>
                    <a href="https://threejs.org/docs/" target="_blank" class="text-red-400 hover:text-red-300">
                        Visit Three.js Docs →
                    </a>
                </div>
                
                <div class="bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">Advanced Three.js</h3>
                    <p class="text-gray-300 mb-4">Shaders, physics, and complex animations</p>
                    <a href="35-threejs-advanced.html" class="text-blue-400 hover:text-blue-300">
                        Advanced Guide →
                    </a>
                </div>
                
                <div class="bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">WebXR</h3>
                    <p class="text-gray-300 mb-4">Virtual and Augmented Reality</p>
                    <a href="40-webxr-basics.html" class="text-purple-400 hover:text-purple-300">
                        WebXR Tutorial →
                    </a>
                </div>
            </div>
            
            <p class="text-gray-400">
                Three.js is constantly evolving with new features and improvements. Stay updated with the latest releases!
            </p>
        </div>
    </section>

    <!-- Scripts -->
    <script src="assets/js/manual-scripts.js"></script>
    
    <script>
        // Initialize GSAP
        gsap.registerPlugin(ScrollTrigger);
        
        // Global variables for demos
        let heroScene, heroCamera, heroRenderer, heroMesh, heroAnimating = true;
        let cubeScene, cubeCamera, cubeRenderer, cubeMesh, cubeAnimating = true;
        let geometriesScene, geometriesCamera, geometriesRenderer, geometriesMeshes = [], geometriesAnimating = true;
        
        // Hero Demo
        function initHeroDemo() {
            const container = document.getElementById('hero-threejs');
            if (!container) return;
            
            heroScene = new THREE.Scene();
            heroCamera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000);
            heroRenderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            
            heroRenderer.setSize(container.offsetWidth, container.offsetHeight);
            heroRenderer.setClearColor(0x000000, 0.1);
            container.appendChild(heroRenderer.domElement);
            
            // Create a torus knot
            const geometry = new THREE.TorusKnotGeometry(1, 0.3, 100, 16);
            const material = new THREE.MeshPhongMaterial({ 
                color: 0x3b82f6,
                shininess: 100
            });
            heroMesh = new THREE.Mesh(geometry, material);
            heroScene.add(heroMesh);
            
            // Add lights
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            heroScene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(1, 1, 1);
            heroScene.add(directionalLight);
            
            heroCamera.position.z = 5;
            
            animateHero();
        }
        
        function animateHero() {
            requestAnimationFrame(animateHero);
            
            if (heroAnimating && heroMesh) {
                heroMesh.rotation.x += 0.01;
                heroMesh.rotation.y += 0.01;
            }
            
            if (heroRenderer && heroScene && heroCamera) {
                heroRenderer.render(heroScene, heroCamera);
            }
        }
        
        function toggleHeroAnimation() {
            heroAnimating = !heroAnimating;
        }
        
        function changeHeroGeometry() {
            if (!heroScene || !heroMesh) return;
            
            const geometries = [
                new THREE.TorusKnotGeometry(1, 0.3, 100, 16),
                new THREE.SphereGeometry(1.5, 32, 32),
                new THREE.BoxGeometry(2, 2, 2),
                new THREE.ConeGeometry(1, 2, 8),
                new THREE.CylinderGeometry(1, 1, 2, 32)
            ];
            
            const randomGeometry = geometries[Math.floor(Math.random() * geometries.length)];
            heroMesh.geometry.dispose();
            heroMesh.geometry = randomGeometry;
        }
        
        // Introduction Demo
        function initIntroDemo() {
            const container = document.getElementById('intro-threejs');
            if (!container) return;
            
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000);
            const renderer = new THREE.WebGLRenderer({ antialias: true });
            
            renderer.setSize(container.offsetWidth, container.offsetHeight);
            renderer.setClearColor(0x1a1a1a);
            container.appendChild(renderer.domElement);
            
            // Create wireframe cube
            const geometry = new THREE.BoxGeometry(2, 2, 2);
            const material = new THREE.MeshBasicMaterial({ 
                color: 0x00ff00,
                wireframe: true
            });
            const cube = new THREE.Mesh(geometry, material);
            scene.add(cube);
            
            camera.position.z = 5;
            
            let frameCount = 0;
            function animate() {
                requestAnimationFrame(animate);
                
                cube.rotation.x += 0.01;
                cube.rotation.y += 0.01;
                
                renderer.render(scene, camera);
                
                // Update stats
                frameCount++;
                if (frameCount % 60 === 0) {
                    const fpsElement = document.getElementById('intro-fps');
                    if (fpsElement) fpsElement.textContent = '60';
                }
            }
            
            animate();
        }
        
        // Cube Demo
        function initCubeDemo() {
            const container = document.getElementById('cube-demo');
            if (!container) return;
            
            cubeScene = new THREE.Scene();
            cubeCamera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000);
            cubeRenderer = new THREE.WebGLRenderer({ antialias: true });
            
            cubeRenderer.setSize(container.offsetWidth, container.offsetHeight);
            cubeRenderer.setClearColor(0x222222);
            container.appendChild(cubeRenderer.domElement);
            
            const geometry = new THREE.BoxGeometry(2, 2, 2);
            const material = new THREE.MeshBasicMaterial({ color: 0x3b82f6 });
            cubeMesh = new THREE.Mesh(geometry, material);
            cubeScene.add(cubeMesh);
            
            cubeCamera.position.z = 5;
            
            animateCube();
        }
        
        function animateCube() {
            requestAnimationFrame(animateCube);
            
            if (cubeAnimating && cubeMesh) {
                cubeMesh.rotation.x += 0.01;
                cubeMesh.rotation.y += 0.01;
            }
            
            if (cubeRenderer && cubeScene && cubeCamera) {
                cubeRenderer.render(cubeScene, cubeCamera);
            }
        }
        
        function toggleCubeAnimation() {
            cubeAnimating = !cubeAnimating;
        }
        
        function changeCubeColor() {
            if (!cubeMesh) return;
            const colors = [0x3b82f6, 0xef4444, 0x10b981, 0xf59e0b, 0x8b5cf6];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            cubeMesh.material.color.setHex(randomColor);
        }
        
        // Geometries Demo
        function initGeometriesDemo() {
            const container = document.getElementById('geometries-demo');
            if (!container) return;
            
            geometriesScene = new THREE.Scene();
            geometriesCamera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000);
            geometriesRenderer = new THREE.WebGLRenderer({ antialias: true });
            
            geometriesRenderer.setSize(container.offsetWidth, container.offsetHeight);
            geometriesRenderer.setClearColor(0x1a1a1a);
            container.appendChild(geometriesRenderer.domElement);
            
            // Add lights
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            geometriesScene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(1, 1, 1);
            geometriesScene.add(directionalLight);
            
            // Create different geometries
            const geometries = [
                new THREE.SphereGeometry(1, 32, 32),
                new THREE.CylinderGeometry(1, 1, 2, 32),
                new THREE.ConeGeometry(1, 2, 8)
            ];
            
            const materials = [
                new THREE.MeshLambertMaterial({ color: 0xff0000 }),
                new THREE.MeshLambertMaterial({ color: 0x00ff00 }),
                new THREE.MeshLambertMaterial({ color: 0x0000ff })
            ];
            
            geometries.forEach((geometry, index) => {
                const mesh = new THREE.Mesh(geometry, materials[index]);
                mesh.position.x = (index - 1) * 3;
                geometriesScene.add(mesh);
                geometriesMeshes.push(mesh);
            });
            
            geometriesCamera.position.z = 8;
            
            animateGeometries();
        }
        
        function animateGeometries() {
            requestAnimationFrame(animateGeometries);
            
            if (geometriesAnimating) {
                geometriesMeshes.forEach((mesh, index) => {
                    mesh.rotation.x += 0.01;
                    mesh.rotation.y += 0.01 * (index + 1);
                });
            }
            
            if (geometriesRenderer && geometriesScene && geometriesCamera) {
                geometriesRenderer.render(geometriesScene, geometriesCamera);
            }
        }
        
        function toggleGeometriesAnimation() {
            geometriesAnimating = !geometriesAnimating;
        }
        
        function addRandomGeometry() {
            if (!geometriesScene) return;
            
            const geometries = [
                new THREE.BoxGeometry(1, 1, 1),
                new THREE.SphereGeometry(0.8, 16, 16),
                new THREE.ConeGeometry(0.8, 1.5, 6),
                new THREE.TorusGeometry(0.8, 0.3, 16, 100)
            ];
            
            const randomGeometry = geometries[Math.floor(Math.random() * geometries.length)];
            const material = new THREE.MeshLambertMaterial({ 
                color: Math.random() * 0xffffff 
            });
            
            const mesh = new THREE.Mesh(randomGeometry, material);
            mesh.position.x = (Math.random() - 0.5) * 10;
            mesh.position.y = (Math.random() - 0.5) * 5;
            mesh.position.z = (Math.random() - 0.5) * 5;
            
            geometriesScene.add(mesh);
            geometriesMeshes.push(mesh);
        }
        
        // Initialize all demos when page loads
        window.addEventListener('load', () => {
            initHeroDemo();
            initIntroDemo();
            initCubeDemo();
            initGeometriesDemo();
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            // Update all renderers and cameras
            const containers = [
                { renderer: heroRenderer, camera: heroCamera, container: document.getElementById('hero-threejs') },
                { renderer: cubeRenderer, camera: cubeCamera, container: document.getElementById('cube-demo') },
                { renderer: geometriesRenderer, camera: geometriesCamera, container: document.getElementById('geometries-demo') }
            ];
            
            containers.forEach(({ renderer, camera, container }) => {
                if (renderer && camera && container) {
                    camera.aspect = container.offsetWidth / container.offsetHeight;
                    camera.updateProjectionMatrix();
                    renderer.setSize(container.offsetWidth, container.offsetHeight);
                }
            });
        });
        
        // Scroll animations
        gsap.from(".demo-container", {
            scrollTrigger: {
                trigger: ".demo-container",
                start: "top 80%",
                toggleActions: "play none none reverse"
            },
            duration: 1,
            y: 50,
            opacity: 0,
            stagger: 0.2
        });
    </script>
</body>
</html> 