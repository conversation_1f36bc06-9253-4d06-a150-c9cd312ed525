<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rotating Text Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .rotating-text-container {
            padding: 8px 12px;
            background: #06b6d4;
            color: black;
            overflow: hidden;
            border-radius: 8px;
            font-size: 24px;
            font-weight: 600;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            min-height: 60px;
        }

        .text-rotate {
            display: flex;
            flex-wrap: wrap;
            white-space: pre-wrap;
            position: relative;
        }

        .text-rotate-sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .text-rotate-word {
            display: inline-flex;
            overflow: hidden;
            padding-bottom: 4px;
        }

        .text-rotate-element {
            display: inline-block;
            transform: translateY(100%);
            opacity: 0;
            will-change: transform, opacity;
            backface-visibility: hidden;
            -webkit-font-smoothing: antialiased;
        }

        .text-rotate-space {
            white-space: pre;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .rotating-text-container {
                padding: 4px 8px;
                font-size: 20px;
                min-height: 50px;
            }
        }

        @media (max-width: 480px) {
            .rotating-text-container {
                padding: 2px 8px;
                font-size: 18px;
                min-height: 45px;
            }
        }
    </style>
</head>
<body>
    <div class="rotating-text-container">
        <span class="text-rotate" id="rotatingText">
            <span class="text-rotate-sr-only">React</span>
        </span>
    </div>

    <script>
        class RotatingText {
            constructor(element, options = {}) {
                this.element = element;
                this.texts = options.texts || ['React', 'Bits', 'Is', 'Cool!'];
                this.rotationInterval = options.rotationInterval || 2000;
                this.staggerDuration = options.staggerDuration || 25;
                this.staggerFrom = options.staggerFrom || 'last';
                this.currentTextIndex = 0;
                this.isAnimating = false;

                // Animation settings for smooth spring-like motion
                this.springConfig = {
                    duration: 600,
                    easing: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
                };

                this.init();
            }

            splitIntoCharacters(text) {
                return Array.from(text);
            }

            getElements(text) {
                const words = text.split(' ');
                return words.map((word, i) => ({
                    characters: this.splitIntoCharacters(word),
                    needsSpace: i !== words.length - 1
                }));
            }

            getStaggerDelay(index, totalChars) {
                const total = totalChars;
                if (this.staggerFrom === 'first') return index * this.staggerDuration;
                if (this.staggerFrom === 'last') return (total - 1 - index) * this.staggerDuration;
                if (this.staggerFrom === 'center') {
                    const center = Math.floor(total / 2);
                    return Math.abs(center - index) * this.staggerDuration;
                }
                return Math.abs(this.staggerFrom - index) * this.staggerDuration;
            }

            createTextElements(text) {
                const elements = this.getElements(text);
                const container = document.createElement('div');
                container.className = 'text-rotate-content';
                container.setAttribute('aria-hidden', 'true');

                let totalCharIndex = 0;
                const totalChars = elements.reduce((sum, word) => sum + word.characters.length, 0);

                elements.forEach((wordObj, wordIndex) => {
                    const wordSpan = document.createElement('span');
                    wordSpan.className = 'text-rotate-word';

                    wordObj.characters.forEach((char, charIndex) => {
                        const charSpan = document.createElement('span');
                        charSpan.className = 'text-rotate-element';
                        charSpan.textContent = char;

                        wordSpan.appendChild(charSpan);
                        totalCharIndex++;
                    });

                    if (wordObj.needsSpace) {
                        const spaceSpan = document.createElement('span');
                        spaceSpan.className = 'text-rotate-space';
                        spaceSpan.textContent = ' ';
                        wordSpan.appendChild(spaceSpan);
                    }

                    container.appendChild(wordSpan);
                });

                return container;
            }

            animateIn(container) {
                const elements = container.querySelectorAll('.text-rotate-element');
                const totalChars = elements.length;

                elements.forEach((element, index) => {
                    const delay = this.getStaggerDelay(index, totalChars);

                    // Use Web Animations API for smoother animations
                    element.animate([
                        {
                            transform: 'translateY(100%)',
                            opacity: 0,
                            offset: 0
                        },
                        {
                            transform: 'translateY(0)',
                            opacity: 1,
                            offset: 1
                        }
                    ], {
                        duration: this.springConfig.duration,
                        easing: this.springConfig.easing,
                        delay: delay,
                        fill: 'forwards'
                    });
                });
            }

            animateOut(container) {
                const elements = container.querySelectorAll('.text-rotate-element');
                const totalChars = elements.length;

                return new Promise(resolve => {
                    let completedAnimations = 0;

                    elements.forEach((element, index) => {
                        const delay = this.getStaggerDelay(index, totalChars);

                        const animation = element.animate([
                            {
                                transform: 'translateY(0)',
                                opacity: 1,
                                offset: 0
                            },
                            {
                                transform: 'translateY(-120%)',
                                opacity: 0,
                                offset: 1
                            }
                        ], {
                            duration: this.springConfig.duration,
                            easing: this.springConfig.easing,
                            delay: delay,
                            fill: 'forwards'
                        });

                        animation.addEventListener('finish', () => {
                            completedAnimations++;
                            if (completedAnimations === elements.length) {
                                resolve();
                            }
                        });
                    });
                });
            }

            async changeText() {
                if (this.isAnimating) return;
                this.isAnimating = true;

                const currentContainer = this.element.querySelector('.text-rotate-content');

                // Animate out current text
                if (currentContainer) {
                    await this.animateOut(currentContainer);
                    currentContainer.remove();
                }

                // Update index
                this.currentTextIndex = (this.currentTextIndex + 1) % this.texts.length;
                const newText = this.texts[this.currentTextIndex];

                // Update screen reader text
                const srText = this.element.querySelector('.text-rotate-sr-only');
                if (srText) srText.textContent = newText;

                // Create and animate in new text
                const newContainer = this.createTextElements(newText);
                this.element.appendChild(newContainer);

                // Trigger animation with a small delay for smoother transition
                await new Promise(resolve => setTimeout(resolve, 50));
                this.animateIn(newContainer);

                // Wait for animation to complete
                await new Promise(resolve => setTimeout(resolve, this.springConfig.duration + 200));
                this.isAnimating = false;
            }

            init() {
                // Create initial text
                const initialText = this.texts[this.currentTextIndex];
                const initialContainer = this.createTextElements(initialText);
                this.element.appendChild(initialContainer);

                // Animate in initial text
                requestAnimationFrame(() => {
                    this.animateIn(initialContainer);
                });

                // Start rotation
                setInterval(() => {
                    this.changeText();
                }, this.rotationInterval);
            }
        }

        // Initialize the rotating text
        document.addEventListener('DOMContentLoaded', () => {
            const rotatingTextElement = document.getElementById('rotatingText');
            new RotatingText(rotatingTextElement, {
                texts: ['React', 'Bits', 'Is', 'Cool!'],
                rotationInterval: 2000,
                staggerDuration: 25,
                staggerFrom: 'last'
            });
        });
    </script>
</body>
</html>
